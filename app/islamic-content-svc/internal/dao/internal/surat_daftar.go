// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SuratDaftarDao is the data access object for table surat_daftar.
type SuratDaftarDao struct {
	table   string             // table is the underlying table name of the DAO.
	group   string             // group is the database configuration group name of current DAO.
	columns SuratDaftarColumns // columns contains all the column names of Table for convenient usage.
}

// SuratDaftarColumns defines and stores column names for table surat_daftar.
type SuratDaftarColumns struct {
	Id          string //
	Nomor       string // 章节编号 (1-114)
	Nama        string // 阿拉伯语章节名
	NamaLatin   string // 拉丁化章节名
	JumlahAyat  string // 经文数量
	TempatTurun string // 降示地点
	Arti        string // 章节含义
	Deskripsi   string // 章节描述
	Audio       string // 音频文件URL
	Status      string // 状态标识
	CreatedAt   string //
	UpdatedAt   string //
	IsPopular   string // 是否热门章节 0否 1是
}

// suratDaftarColumns holds the columns for table surat_daftar.
var suratDaftarColumns = SuratDaftarColumns{
	Id:          "id",
	Nomor:       "nomor",
	Nama:        "nama",
	NamaLatin:   "nama_latin",
	JumlahAyat:  "jumlah_ayat",
	TempatTurun: "tempat_turun",
	Arti:        "arti",
	Deskripsi:   "deskripsi",
	Audio:       "audio",
	Status:      "status",
	CreatedAt:   "created_at",
	UpdatedAt:   "updated_at",
	IsPopular:   "is_popular",
}

// NewSuratDaftarDao creates and returns a new DAO object for table data access.
func NewSuratDaftarDao() *SuratDaftarDao {
	return &SuratDaftarDao{
		group:   "default",
		table:   "surat_daftar",
		columns: suratDaftarColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SuratDaftarDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SuratDaftarDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SuratDaftarDao) Columns() SuratDaftarColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SuratDaftarDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SuratDaftarDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SuratDaftarDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
