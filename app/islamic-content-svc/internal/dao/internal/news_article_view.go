// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsArticleViewDao is the data access object for table news_article_view.
type NewsArticleViewDao struct {
	table   string                 // table is the underlying table name of the DAO.
	group   string                 // group is the database configuration group name of current DAO.
	columns NewsArticleViewColumns // columns contains all the column names of Table for convenient usage.
}

// NewsArticleViewColumns defines and stores column names for table news_article_view.
type NewsArticleViewColumns struct {
	Id          string //
	UserId      string // 用户id
	ArticleId   string // article_id
	ArticleName string // 名称
	CreateTime  string // 创建时间（注册时间）
	UpdateTime  string // 更新时间，0代表创建后未更新
}

// newsArticleViewColumns holds the columns for table news_article_view.
var newsArticleViewColumns = NewsArticleViewColumns{
	Id:          "id",
	UserId:      "user_id",
	ArticleId:   "article_id",
	ArticleName: "article_name",
	CreateTime:  "create_time",
	UpdateTime:  "update_time",
}

// NewNewsArticleViewDao creates and returns a new DAO object for table data access.
func NewNewsArticleViewDao() *NewsArticleViewDao {
	return &NewsArticleViewDao{
		group:   "default",
		table:   "news_article_view",
		columns: newsArticleViewColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *NewsArticleViewDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *NewsArticleViewDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *NewsArticleViewDao) Columns() NewsArticleViewColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *NewsArticleViewDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *NewsArticleViewDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *NewsArticleViewDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
