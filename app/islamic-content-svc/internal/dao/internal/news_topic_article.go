// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsTopicArticleDao is the data access object for table news_topic_article.
type NewsTopicArticleDao struct {
	table   string                  // table is the underlying table name of the DAO.
	group   string                  // group is the database configuration group name of current DAO.
	columns NewsTopicArticleColumns // columns contains all the column names of Table for convenient usage.
}

// NewsTopicArticleColumns defines and stores column names for table news_topic_article.
type NewsTopicArticleColumns struct {
	Id          string //
	TopicId     string // topic id
	ArticleId   string // 文章id
	ArticleName string // 文章name
	CreateTime  string // 创建时间
	UpdateTime  string // 修改时间
	DeleteTime  string // 删除时间
}

// newsTopicArticleColumns holds the columns for table news_topic_article.
var newsTopicArticleColumns = NewsTopicArticleColumns{
	Id:          "id",
	TopicId:     "topic_id",
	ArticleId:   "article_id",
	ArticleName: "article_name",
	CreateTime:  "create_time",
	UpdateTime:  "update_time",
	DeleteTime:  "delete_time",
}

// NewNewsTopicArticleDao creates and returns a new DAO object for table data access.
func NewNewsTopicArticleDao() *NewsTopicArticleDao {
	return &NewsTopicArticleDao{
		group:   "default",
		table:   "news_topic_article",
		columns: newsTopicArticleColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *NewsTopicArticleDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *NewsTopicArticleDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *NewsTopicArticleDao) Columns() NewsTopicArticleColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *NewsTopicArticleDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *NewsTopicArticleDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *NewsTopicArticleDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
