// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsArticleLanguageDao is the data access object for table news_article_language.
type NewsArticleLanguageDao struct {
	table   string                     // table is the underlying table name of the DAO.
	group   string                     // group is the database configuration group name of current DAO.
	columns NewsArticleLanguageColumns // columns contains all the column names of Table for convenient usage.
}

// NewsArticleLanguageColumns defines and stores column names for table news_article_language.
type NewsArticleLanguageColumns struct {
	Id         string //
	ArticleId  string // 文章id
	LanguageId string // 语言id,0-中文，1-英文，2-印尼语
	Name       string // 名称
	Content    string // 正文
	CreateTime string // 创建时间
	UpdateTime string // 修改时间
	DeleteTime string // 删除时间
}

// newsArticleLanguageColumns holds the columns for table news_article_language.
var newsArticleLanguageColumns = NewsArticleLanguageColumns{
	Id:         "id",
	ArticleId:  "article_id",
	LanguageId: "language_id",
	Name:       "name",
	Content:    "content",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	DeleteTime: "delete_time",
}

// NewNewsArticleLanguageDao creates and returns a new DAO object for table data access.
func NewNewsArticleLanguageDao() *NewsArticleLanguageDao {
	return &NewsArticleLanguageDao{
		group:   "default",
		table:   "news_article_language",
		columns: newsArticleLanguageColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *NewsArticleLanguageDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *NewsArticleLanguageDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *NewsArticleLanguageDao) Columns() NewsArticleLanguageColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *NewsArticleLanguageDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *NewsArticleLanguageDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *NewsArticleLanguageDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
