// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsTopicLanguageDao is the data access object for table news_topic_language.
type NewsTopicLanguageDao struct {
	table   string                   // table is the underlying table name of the DAO.
	group   string                   // group is the database configuration group name of current DAO.
	columns NewsTopicLanguageColumns // columns contains all the column names of Table for convenient usage.
}

// NewsTopicLanguageColumns defines and stores column names for table news_topic_language.
type NewsTopicLanguageColumns struct {
	Id         string //
	TopicId    string // 专题id
	LanguageId string // 语言id,0-中文，1-英文，2-印尼语
	Name       string // 名称
	ShortName  string // 名称
	CreateTime string // 创建时间
	UpdateTime string // 修改时间
	DeleteTime string // 删除时间
}

// newsTopicLanguageColumns holds the columns for table news_topic_language.
var newsTopicLanguageColumns = NewsTopicLanguageColumns{
	Id:         "id",
	TopicId:    "topic_id",
	LanguageId: "language_id",
	Name:       "name",
	ShortName:  "short_name",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	DeleteTime: "delete_time",
}

// NewNewsTopicLanguageDao creates and returns a new DAO object for table data access.
func NewNewsTopicLanguageDao() *NewsTopicLanguageDao {
	return &NewsTopicLanguageDao{
		group:   "default",
		table:   "news_topic_language",
		columns: newsTopicLanguageColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *NewsTopicLanguageDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *NewsTopicLanguageDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *NewsTopicLanguageDao) Columns() NewsTopicLanguageColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *NewsTopicLanguageDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *NewsTopicLanguageDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *NewsTopicLanguageDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
