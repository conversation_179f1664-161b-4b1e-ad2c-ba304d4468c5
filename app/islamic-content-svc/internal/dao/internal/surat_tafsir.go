// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SuratTafsirDao is the data access object for table surat_tafsir.
type SuratTafsirDao struct {
	table   string             // table is the underlying table name of the DAO.
	group   string             // group is the database configuration group name of current DAO.
	columns SuratTafsirColumns // columns contains all the column names of Table for convenient usage.
}

// SuratTafsirColumns defines and stores column names for table surat_tafsir.
type SuratTafsirColumns struct {
	Id        string //
	TafsirId  string // 注释全局ID
	SurahId   string // 所属章节ID
	AyatNomor string // 对应经文编号
	Tafsir    string // 注释内容
	CreatedAt string //
	UpdatedAt string //
}

// suratTafsirColumns holds the columns for table surat_tafsir.
var suratTafsirColumns = SuratTafsirColumns{
	Id:        "id",
	TafsirId:  "tafsir_id",
	SurahId:   "surah_id",
	AyatNomor: "ayat_nomor",
	Tafsir:    "tafsir",
	CreatedAt: "created_at",
	UpdatedAt: "updated_at",
}

// NewSuratTafsirDao creates and returns a new DAO object for table data access.
func NewSuratTafsirDao() *SuratTafsirDao {
	return &SuratTafsirDao{
		group:   "default",
		table:   "surat_tafsir",
		columns: suratTafsirColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SuratTafsirDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SuratTafsirDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SuratTafsirDao) Columns() SuratTafsirColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SuratTafsirDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SuratTafsirDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SuratTafsirDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
