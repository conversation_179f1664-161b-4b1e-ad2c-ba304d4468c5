// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsArticleCollectDao is the data access object for table news_article_collect.
type NewsArticleCollectDao struct {
	table   string                    // table is the underlying table name of the DAO.
	group   string                    // group is the database configuration group name of current DAO.
	columns NewsArticleCollectColumns // columns contains all the column names of Table for convenient usage.
}

// NewsArticleCollectColumns defines and stores column names for table news_article_collect.
type NewsArticleCollectColumns struct {
	Id          string //
	UserId      string // 用户id
	ArticleId   string // article_id
	ArticleName string // 名称
	CreateTime  string // 创建时间（注册时间）
	UpdateTime  string // 更新时间，0代表创建后未更新
}

// newsArticleCollectColumns holds the columns for table news_article_collect.
var newsArticleCollectColumns = NewsArticleCollectColumns{
	Id:          "id",
	UserId:      "user_id",
	ArticleId:   "article_id",
	ArticleName: "article_name",
	CreateTime:  "create_time",
	UpdateTime:  "update_time",
}

// NewNewsArticleCollectDao creates and returns a new DAO object for table data access.
func NewNewsArticleCollectDao() *NewsArticleCollectDao {
	return &NewsArticleCollectDao{
		group:   "default",
		table:   "news_article_collect",
		columns: newsArticleCollectColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *NewsArticleCollectDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *NewsArticleCollectDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *NewsArticleCollectDao) Columns() NewsArticleCollectColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *NewsArticleCollectDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *NewsArticleCollectDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *NewsArticleCollectDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
