// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// SurahReadRecordDao is the data access object for table surah_read_record.
type SurahReadRecordDao struct {
	table   string                 // table is the underlying table name of the DAO.
	group   string                 // group is the database configuration group name of current DAO.
	columns SurahReadRecordColumns // columns contains all the column names of Table for convenient usage.
}

// SurahReadRecordColumns defines and stores column names for table surah_read_record.
type SurahReadRecordColumns struct {
	Id         string //
	UserId     string // 用户id
	AyahId     string // ayah_id节id
	SurahName  string // 名称
	IsUserOp   string // 是否用户操作，1是 0否
	CreateTime string // 创建时间（注册时间）
	UpdateTime string // 更新时间，0代表创建后未更新
}

// surahReadRecordColumns holds the columns for table surah_read_record.
var surahReadRecordColumns = SurahReadRecordColumns{
	Id:         "id",
	UserId:     "user_id",
	AyahId:     "ayah_id",
	SurahName:  "surah_name",
	IsUserOp:   "is_user_op",
	CreateTime: "create_time",
	UpdateTime: "update_time",
}

// NewSurahReadRecordDao creates and returns a new DAO object for table data access.
func NewSurahReadRecordDao() *SurahReadRecordDao {
	return &SurahReadRecordDao{
		group:   "default",
		table:   "surah_read_record",
		columns: surahReadRecordColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *SurahReadRecordDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *SurahReadRecordDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *SurahReadRecordDao) Columns() SurahReadRecordColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *SurahReadRecordDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *SurahReadRecordDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *SurahReadRecordDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
