// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsCategoryLanguageDao is the data access object for table news_category_language.
type NewsCategoryLanguageDao struct {
	table   string                      // table is the underlying table name of the DAO.
	group   string                      // group is the database configuration group name of current DAO.
	columns NewsCategoryLanguageColumns // columns contains all the column names of Table for convenient usage.
}

// NewsCategoryLanguageColumns defines and stores column names for table news_category_language.
type NewsCategoryLanguageColumns struct {
	Id         string //
	CategoryId string // 资讯分类id
	LanguageId string // 语言id,0-中文，1-英文，2-印尼语
	Name       string // 名称
	CreateTime string // 创建时间
	UpdateTime string // 修改时间
	DeleteTime string // 删除时间
}

// newsCategoryLanguageColumns holds the columns for table news_category_language.
var newsCategoryLanguageColumns = NewsCategoryLanguageColumns{
	Id:         "id",
	CategoryId: "category_id",
	LanguageId: "language_id",
	Name:       "name",
	CreateTime: "create_time",
	UpdateTime: "update_time",
	DeleteTime: "delete_time",
}

// NewNewsCategoryLanguageDao creates and returns a new DAO object for table data access.
func NewNewsCategoryLanguageDao() *NewsCategoryLanguageDao {
	return &NewsCategoryLanguageDao{
		group:   "default",
		table:   "news_category_language",
		columns: newsCategoryLanguageColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *NewsCategoryLanguageDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *NewsCategoryLanguageDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *NewsCategoryLanguageDao) Columns() NewsCategoryLanguageColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *NewsCategoryLanguageDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *NewsCategoryLanguageDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *NewsCategoryLanguageDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
