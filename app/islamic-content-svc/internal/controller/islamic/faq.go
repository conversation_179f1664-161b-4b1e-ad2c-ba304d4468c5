package islamic

import (
	"context"
	"github.com/gogf/gf/v2/frame/g"
	v1 "halalplus/app/islamic-content-svc/api/islamic/v1"
	"halalplus/app/islamic-content-svc/internal/service"
)

type ControllerFaq struct {
	v1.UnimplementedFaqServiceServer
}

func (*ControllerFaq) FaqCategoryList(ctx context.Context, req *v1.FaqCateListReq) (res *v1.FaqCateListRes, err error) {

	items, err := service.Faq().CateList(ctx, req.LanguageId)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res = &v1.FaqCateListRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.FaqCateListData{
			List: items,
		},
	}
	return res, nil
}

func (*ControllerFaq) FaqListByCateId(ctx context.Context, req *v1.FaqListByCateIdReq) (res *v1.FaqListByCateIdRes, err error) {

	items, err := service.Faq().FaqList(ctx, req.LanguageId, req.CateId, req.Keyword)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res = &v1.FaqListByCateIdRes{
		Code: 200,
		Msg:  "success",
		Data: &v1.FaqListByCateIdData{
			List: items,
		},
	}
	return res, nil
}

func (*ControllerFaq) FaqOne(ctx context.Context, req *v1.FaqOneReq) (res *v1.FaqOneRes, err error) {
	items, err := service.Faq().FaqOne(ctx, req.LanguageId, req.Id)
	if err != nil {
		g.Log().Error(ctx, err)
		return nil, err
	}
	res = &v1.FaqOneRes{
		Code: 200,
		Msg:  "success",
		Data: items,
	}
	return res, nil
}
