// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

import (
	"github.com/gogf/gf/v2/os/gtime"
)

// WisdomCate is the golang structure for table wisdom_cate.
type WisdomCate struct {
	Id            uint        `json:"id"            orm:"id"             description:""`                 //
	IsOpen        int         `json:"isOpen"        orm:"is_open"        description:"状态 [ 1 启用  2 禁用]"` // 状态 [ 1 启用  2 禁用]
	Sort          int         `json:"sort"          orm:"sort"           description:"排序"`               // 排序
	CateCount     int         `json:"cateCount"     orm:"cate_count"     description:"分类下的文章总数"`         // 分类下的文章总数
	CreatedAt     *gtime.Time `json:"createdAt"     orm:"created_at"     description:"创建时间"`             // 创建时间
	CreateAccount string      `json:"createAccount" orm:"create_account" description:"创建者"`              // 创建者
	UpdatedAt     *gtime.Time `json:"updatedAt"     orm:"updated_at"     description:"更新时间"`             // 更新时间
	UpdateAccount string      `json:"updateAccount" orm:"update_account" description:"更新者"`              // 更新者
}
