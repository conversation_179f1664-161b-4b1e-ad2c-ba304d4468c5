syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";
import "pbentity/news_article.proto";
import "pbentity/news_article_language.proto";
import "pbentity/news_category.proto";
import "pbentity/news_category_language.proto";
import "pbentity/news_topic.proto";
import "pbentity/news_topic_language.proto";
import "common/front_info.proto";
import "common/base.proto";
import "google/protobuf/wrappers.proto";

message NewsCategoryListReq {
  uint32 language_id = 1;   // 语言id
  uint32 pid = 2;  //父类id
}



message CategoryInfo {
  uint32 id = 1;
  uint32 parent_id = 2;
  uint32 language_id = 3;
  string name = 4;
  string cover_imgs = 5;
}
message NewsCategoryListResData {
  repeated CategoryInfo list = 1;
}
message NewsCategoryListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  NewsCategoryListResData data = 4;
}


message NewsListByCateIdReq {
  uint32 cate_id = 1;   // 分类id
  uint32 language_id = 2;   // 语言id
  uint32 is_recommend = 3;   // 是否推荐,0-否,1-是
  common.PageRequest page = 4;  // 分页参数

}

message NewsListByCateIdResData {
  repeated ArticleInfo list = 1;
  common.PageResponse page = 2;  // 分页参数

}
message NewsListByCateIdRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  NewsListByCateIdResData data = 4;
}

message NewsHotListReq {
 uint32 language_id = 1;   // 语言id
 uint32 is_hot = 2;   // 是否热门3条数据 0-否,1-是
  common.PageRequest page = 3;  // 分页参数


}

message NewsHotListResData {
  repeated ArticleInfo list = 1;
  common.PageResponse page = 2;  // 分页参数

}
message NewsHotListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  NewsHotListResData data = 4;
}

message NewsInfoReq {
  int32 article_id = 1;
  uint32 language_id = 2;   // 语言id

}
message NewsInfoRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  ArticleInfo data = 4;
}



message NewsCollectReq {
  uint32 language_id = 1;   // 语言id
  common.PageRequest page = 2;  // 分页参数

}

message NewsCollectResData {
  repeated ArticleInfo list = 1;
  common.PageResponse page = 2;  // 分页参数

}

message NewsCollectRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  NewsCollectResData data = 4;

}
message NewsCollectStatusCheckReq {
  int32 article_id = 1;
}
message NewsCollectStatusCheckRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  int32 is_collect = 4;
}

message NewsCollectOpReq {
  int32 article_id = 1;
}
message NewsCollectOpRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}


message NewsShareOpReq {
  int32 article_id = 1;
}
message NewsShareOpRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

message NewsViewOpReq {
  int32 article_id = 1;
}
message NewsViewOpRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

message NewsTopicListReq {
  uint32 language_id = 1;   // 语言id
}


message TopicInfo {
  uint32 topic_id = 1;               // 用户id
  uint32 language_id = 2;               // 用户id
  string name = 3;          // 账号
  string short_name = 4;          // 账号
  string topic_imgs  = 5;  // 专题图片


}


message NewsTopicListResData {
  repeated TopicInfo list = 1;
}

message NewsTopicListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  NewsTopicListResData data = 4; // 专题信息
}

message NewsListByTopicIdReq {
  uint32 topic_id = 1;   // 话题id
  uint32 language_id = 2;   // 语言id
  common.PageRequest page = 3;  // 分页参数

}



message ArticleInfo {
  uint32 article_id = 1; // 文章id
  uint32 language_id = 2;// 语言id
  string name = 3; // 文章标题
  string content = 4;  // 文章内容
  uint32 category_id  = 5;  // 分类id
  string category_name   = 6;  // 分类名称
  string cover_imgs   = 7;  // 专题图片
  string author      = 8; // 创建人
  int64  publish_time = 9; // 发布时间
  string  author_logo = 10; // 发布时间
  uint32  author_auth_status = 11; // 发布时间
}

message NewsListByTopicIdResData {
  string topic_name = 4;
  repeated ArticleInfo list = 1;
  common.PageResponse page = 2;  // 分页参数

}
message NewsListByTopicIdRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  NewsListByTopicIdResData data = 5;
}

service NewsService {
  // 新闻分类列表-done
rpc NewsCategoryList(NewsCategoryListReq) returns (NewsCategoryListRes);
  // 新闻列表通过分类ID-done
rpc NewsListByCateId(NewsListByCateIdReq) returns (NewsListByCateIdRes);
//新闻专题列表-done
rpc NewsTopicList(NewsTopicListReq) returns (NewsTopicListRes);
// 新闻列表通过专题ID
rpc NewsListByTopicId(NewsListByTopicIdReq) returns (NewsListByTopicIdRes);
// 新闻详情
rpc NewsInfo(NewsInfoReq) returns (NewsInfoRes);


//热门新闻列表
rpc NewsHotList(NewsHotListReq) returns (NewsHotListRes);
// 新闻收藏列表
rpc NewsCollectList(NewsCollectReq) returns (NewsCollectRes);
// 新闻收藏状态检查
rpc NewsCollectStatusCheck(NewsCollectStatusCheckReq) returns (NewsCollectStatusCheckRes);
// 新闻收藏操作
rpc NewsCollectOp(NewsCollectOpReq) returns (NewsCollectOpRes);
  // 新闻分享操作
  rpc NewsShareOp(NewsShareOpReq) returns (NewsShareOpRes);
  // 新闻浏览操作
  rpc NewsViewOp(NewsViewOpReq) returns (NewsViewOpRes);

}
