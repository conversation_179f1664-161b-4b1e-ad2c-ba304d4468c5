syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";
import "common/base.proto";
import "pbentity/banner.proto";
import "google/protobuf/wrappers.proto";

// Banner列表请求
message BannerListReq {
  uint32 language_id = 1;   // 语言ID: 0-中文, 1-英文, 2-印尼语
}

// Banner信息
message BannerInfo {
  uint32 id = 1;            // Banner ID
  uint32 language_id = 2;   // 语言ID
  string title = 3;         // 广告标题
  string description = 4;   // 广告描述
  string image_url = 5;     // 广告图片URL
  string link_url = 6;      // 跳转链接URL
  uint32 sort_order = 7;    // 排序权重
}

// Banner列表数据
message BannerListData {
  repeated BannerInfo list = 1;
}

// Banner列表响应
message BannerListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  BannerListData data = 4;
}

// Banner点击统计请求
message BannerClickReq {
  uint32 banner_id = 1;     // Banner ID
  string device_id = 2;     // 设备唯一标识
}

// Banner点击统计响应
message BannerClickRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

// Banner服务定义
service BannerService {
  // 获取Banner列表
  rpc BannerList(BannerListReq) returns (BannerListRes);

  // 记录Banner点击统计
  rpc BannerClick(BannerClickReq) returns (BannerClickRes);
}