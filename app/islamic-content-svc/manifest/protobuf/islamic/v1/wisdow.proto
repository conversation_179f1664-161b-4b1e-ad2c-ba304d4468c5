syntax = "proto3";

package islamic.v1;
option go_package = "halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1";

import "common/base.proto";

// 名言标签列表请求
message WisdomCateReq {
  uint32 language_id = 1;   // 语言id
}

// 名言标签列表数据
message WisdomCateListData {
  repeated WisdomCateItem list = 1;
}

// 名言标签列表响应
message WisdomCateRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  WisdomCateListData data = 4; // 名言标签列表
}


// 名言标签item
message WisdomCateItem {
  uint32 id         = 1;  // 分类ID
  int32  is_open    = 2;  // 是否启用
  int32  sort       = 3;  // 排序
  int32  cate_count = 4;  // 该分类下的数量
  string title      = 5;  // 分类名称
  uint32  language_id = 6;// 语言id
}



// 名言列表请求
message WisdomListReq {
  uint32 language_id = 1;   // 语言id
  uint32 cate_id = 2;       // 分类id
  string keyword = 3;       // 搜索关键字
}

message WisdomListData {
  repeated WisdomListItem list = 1;
}

// 名言列表响应
message WisdomListRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  WisdomListData data = 4; // 名言列表
}

// 名言item
message WisdomListItem {
  uint32 id         = 1;    // 问题ID
  int32  is_open    = 2;    // 是否启用
  int32  sort       = 3;    // 排序
  int32  views      = 4;    // 浏览次数
  string title      = 5;    // 问题标题
  string desc       = 6;    // 问题描述
  int32  language_id = 7;   // 语言id
  uint32 wisdom_cate_id = 8;   // 分类id
  string image_url = 9;     // 图片
}

// 名言详情请求
message WisdomOneReq {
  uint32 language_id = 1;   // 语言id
  uint32 id = 2;            // id
}

// 名言详情响应
message WisdomOneRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  WisdomOneItem data = 5;
}

// 名言详情item
message WisdomOneItem{
  uint32 id         = 1;  // 问题ID
  int32  is_open    = 2;  // 是否启用
  int32  sort       = 3;  // 排序
  int32  views      = 4;  // 浏览次数
  string title      = 5;  // 问题标题
  string desc       = 6;  // 问题描述
  int32  language_id = 7; // 语言id
  uint32 wisdom_cate_id = 8; // 分类id
  string wisdom_cate_title = 9;// 分类名称
  string image_url = 10;     // 图片
}

// 名言服务定义
service WisdomService {
  // 名言分类列表
  rpc WisdomCateList(WisdomCateReq) returns (WisdomCateRes);
  // 名言列表
  rpc WisdomList(WisdomListReq) returns (WisdomListRes);
  // 名言详情
  rpc WisdomOne(WisdomOneReq) returns (WisdomOneRes);
}