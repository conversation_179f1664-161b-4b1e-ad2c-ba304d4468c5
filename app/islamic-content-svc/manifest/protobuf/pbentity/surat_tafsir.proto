// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";

import "google/protobuf/timestamp.proto";

message SuratTafsir {
    int32                     Id        = 1; //               
    int32                     TafsirId  = 2; // 注释全局ID    
    int32                     SurahId   = 3; // 所属章节ID    
    int32                     AyatNomor = 4; // 对应经文编号  
    string                    Tafsir    = 5; // 注释内容      
    google.protobuf.Timestamp CreatedAt = 6; //               
    google.protobuf.Timestamp UpdatedAt = 7; //               
}