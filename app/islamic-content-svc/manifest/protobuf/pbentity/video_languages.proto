// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";

import "google/protobuf/timestamp.proto";

message VideoLanguages {
    uint32                    Id          = 1; // 主键ID
    uint32                    VideoId     = 2; // 视频ID
    uint32                    LanguageId  = 3; // 语言ID：0-中文，1-英文，2-印尼语
    string                    Title       = 4; // 视频标题
    string                    Description = 5; // 视频描述(富文本)
    google.protobuf.Timestamp CreatedAt   = 6; // 创建时间
    google.protobuf.Timestamp UpdatedAt   = 7; // 更新时间
}