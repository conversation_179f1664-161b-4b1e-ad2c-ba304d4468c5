// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";

import "google/protobuf/timestamp.proto";

message Videos {
    uint32                    Id               = 1;  // 主键ID
    uint32                    CategoryId       = 2;  // 分类ID
    string                    VideoUrl         = 3;  // 视频文件URL
    uint64                    VideoSize        = 4;  // 视频文件大小(字节)
    uint32                    VideoDuration    = 5;  // 视频时长(秒)
    string                    VideoFormat      = 6;  // 视频格式：mp4, mov等
    uint64                    ViewCount        = 7;  // 播放次数
    uint64                    ShareCount       = 8;  // 分享次数
    uint64                    CollectCount     = 9;  // 收藏次数
    string                    CreatorName      = 10; // 创建者姓名
    string                    Author           = 11; // 视频作者
    string                    AuthorLogo       = 12; // 作者头像URL
    uint32                    AuthorAuthStatus = 13; // 作者认证状态：0-未认证，1-已认证
    uint32                    PublishState     = 14; // 发布状态：0-待发布，1-已发布，2-已下线
    uint32                    IsRecommended    = 15; // 是否推荐，0-否，1-是
    google.protobuf.Timestamp CreatedAt        = 16; // 创建时间
    google.protobuf.Timestamp PublishedAt      = 17; // 发布时间
    google.protobuf.Timestamp UpdatedAt        = 18; // 更新时间
}