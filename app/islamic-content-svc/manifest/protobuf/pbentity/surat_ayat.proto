// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";

import "google/protobuf/timestamp.proto";

message SuratAyat {
    int32                     Id        = 1;  //                     
    int32                     AyatId    = 2;  // 经文全局ID          
    int32                     SurahId   = 3;  // 所属章节ID          
    int32                     Nomor     = 4;  // 经文在章节中的编号  
    string                    Ar        = 5;  // 阿拉伯语经文        
    string                    Tr        = 6;  // 音译文本            
    string                    Idn       = 7;  // 印尼语翻译          
    google.protobuf.Timestamp CreatedAt = 8;  //                     
    google.protobuf.Timestamp UpdatedAt = 9;  //                     
    int32                     Juz       = 10; // juz编号             
    int32                     Page      = 11; // 所在页码            
}