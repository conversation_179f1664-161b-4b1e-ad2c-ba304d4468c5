// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";

import "google/protobuf/timestamp.proto";

message VideoPlaylists {
    uint32                    Id           = 1;  // 主键ID
    string                    CoverUrl     = 2;  // 专题封面图片链接
    uint32                    IsVisible    = 3;  // 是否显示，0-隐藏，1-显示
    uint32                    SortOrder    = 4;  // 排序权重，数字越小越靠前
    uint32                    VideoCount   = 5;  // 播放列表下视频数量
    uint64                    ViewCount    = 6;  // 播放列表浏览次数
    uint64                    ShareCount   = 7;  // 播放列表分享次数
    uint64                    CollectCount = 8;  // 播放列表收藏次数
    google.protobuf.Timestamp CreatedAt    = 9;  // 创建时间
    google.protobuf.Timestamp UpdatedAt    = 10; // 更新时间
}