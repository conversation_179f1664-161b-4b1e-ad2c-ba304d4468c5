// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";

import "google/protobuf/timestamp.proto";

message VideoPlaylistRelations {
    uint32                    Id         = 1; // 主键ID
    uint32                    PlaylistId = 2; // 播放列表ID
    uint32                    VideoId    = 3; // 视频ID
    uint32                    SortOrder  = 4; // 排序权重，数字越小越靠前
    google.protobuf.Timestamp CreatedAt  = 5; // 创建时间
    google.protobuf.Timestamp UpdatedAt  = 6; // 更新时间
}