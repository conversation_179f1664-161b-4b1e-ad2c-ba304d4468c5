// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";

import "google/protobuf/timestamp.proto";

message VideoCollects {
    uint64                    Id        = 1; // 主键ID
    uint64                    UserId    = 2; // 用户ID
    uint32                    VideoId   = 3; // 视频ID
    google.protobuf.Timestamp CreatedAt = 4; // 收藏时间
}