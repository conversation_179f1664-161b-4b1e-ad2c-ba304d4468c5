// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

syntax = "proto3";

package pbentity;

option go_package = "halalplus/app/islamic-content-svc/api/pbentity";

import "google/protobuf/timestamp.proto";

message VideoCategories {
    uint32                    Id         = 1; // 主键ID
    uint32                    VideoCount = 2; // 分类下视频数量
    string                    Remark     = 3; // 备注
    google.protobuf.Timestamp CreatedAt  = 4; // 创建时间
    google.protobuf.Timestamp UpdatedAt  = 5; // 更新时间
}