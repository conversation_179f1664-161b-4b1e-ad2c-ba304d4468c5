// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/banner_stats.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BannerStats struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                  // 主键ID
	BannerId  uint32                 `protobuf:"varint,2,opt,name=BannerId,proto3" json:"BannerId,omitempty" dc:"广告ID"`      // 广告ID
	UserId    uint64                 `protobuf:"varint,3,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"用户ID，0表示未登录用户"` // 用户ID，0表示未登录用户
	DeviceId  string                 `protobuf:"bytes,4,opt,name=DeviceId,proto3" json:"DeviceId,omitempty" dc:"设备唯一标识"`     // 设备唯一标识
	IpAddress string                 `protobuf:"bytes,5,opt,name=IpAddress,proto3" json:"IpAddress,omitempty" dc:"IP地址"`     // IP地址
	UserAgent string                 `protobuf:"bytes,6,opt,name=UserAgent,proto3" json:"UserAgent,omitempty" dc:"用户代理信息"`   // 用户代理信息
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty" dc:"操作时间"`     // 操作时间
}

func (x *BannerStats) Reset() {
	*x = BannerStats{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_banner_stats_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BannerStats) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BannerStats) ProtoMessage() {}

func (x *BannerStats) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_banner_stats_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BannerStats.ProtoReflect.Descriptor instead.
func (*BannerStats) Descriptor() ([]byte, []int) {
	return file_pbentity_banner_stats_proto_rawDescGZIP(), []int{0}
}

func (x *BannerStats) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *BannerStats) GetBannerId() uint32 {
	if x != nil {
		return x.BannerId
	}
	return 0
}

func (x *BannerStats) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *BannerStats) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *BannerStats) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

func (x *BannerStats) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *BannerStats) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_pbentity_banner_stats_proto protoreflect.FileDescriptor

var file_pbentity_banner_stats_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x62, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70,
	0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe3, 0x01, 0x0a, 0x0b, 0x42, 0x61, 0x6e,
	0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x42, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x42, 0x61, 0x6e, 0x6e,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x06, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x44, 0x65, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x49, 0x70, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x49, 0x70, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x55, 0x73, 0x65, 0x72, 0x41, 0x67,
	0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x67, 0x65, 0x6e, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x30,
	0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f,
	0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d,
	0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_banner_stats_proto_rawDescOnce sync.Once
	file_pbentity_banner_stats_proto_rawDescData = file_pbentity_banner_stats_proto_rawDesc
)

func file_pbentity_banner_stats_proto_rawDescGZIP() []byte {
	file_pbentity_banner_stats_proto_rawDescOnce.Do(func() {
		file_pbentity_banner_stats_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_banner_stats_proto_rawDescData)
	})
	return file_pbentity_banner_stats_proto_rawDescData
}

var file_pbentity_banner_stats_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_banner_stats_proto_goTypes = []interface{}{
	(*BannerStats)(nil),           // 0: pbentity.BannerStats
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_pbentity_banner_stats_proto_depIdxs = []int32{
	1, // 0: pbentity.BannerStats.CreatedAt:type_name -> google.protobuf.Timestamp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_pbentity_banner_stats_proto_init() }
func file_pbentity_banner_stats_proto_init() {
	if File_pbentity_banner_stats_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_banner_stats_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BannerStats); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_banner_stats_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_banner_stats_proto_goTypes,
		DependencyIndexes: file_pbentity_banner_stats_proto_depIdxs,
		MessageInfos:      file_pbentity_banner_stats_proto_msgTypes,
	}.Build()
	File_pbentity_banner_stats_proto = out.File
	file_pbentity_banner_stats_proto_rawDesc = nil
	file_pbentity_banner_stats_proto_goTypes = nil
	file_pbentity_banner_stats_proto_depIdxs = nil
}
