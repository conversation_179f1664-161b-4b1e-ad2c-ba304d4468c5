// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/video_playlist_relations.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VideoPlaylistRelations struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                       // 主键ID
	PlaylistId    uint32                 `protobuf:"varint,2,opt,name=PlaylistId,proto3" json:"PlaylistId,omitempty" dc:"播放列表ID"`     // 播放列表ID
	VideoId       uint32                 `protobuf:"varint,3,opt,name=VideoId,proto3" json:"VideoId,omitempty" dc:"视频ID"`             // 视频ID
	SortOrder     uint32                 `protobuf:"varint,4,opt,name=SortOrder,proto3" json:"SortOrder,omitempty" dc:"排序权重，数字越小越靠前"` // 排序权重，数字越小越靠前
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty" dc:"创建时间"`          // 创建时间
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" dc:"更新时间"`          // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlaylistRelations) Reset() {
	*x = VideoPlaylistRelations{}
	mi := &file_pbentity_video_playlist_relations_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlaylistRelations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlaylistRelations) ProtoMessage() {}

func (x *VideoPlaylistRelations) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_video_playlist_relations_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlaylistRelations.ProtoReflect.Descriptor instead.
func (*VideoPlaylistRelations) Descriptor() ([]byte, []int) {
	return file_pbentity_video_playlist_relations_proto_rawDescGZIP(), []int{0}
}

func (x *VideoPlaylistRelations) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VideoPlaylistRelations) GetPlaylistId() uint32 {
	if x != nil {
		return x.PlaylistId
	}
	return 0
}

func (x *VideoPlaylistRelations) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *VideoPlaylistRelations) GetSortOrder() uint32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *VideoPlaylistRelations) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VideoPlaylistRelations) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_pbentity_video_playlist_relations_proto protoreflect.FileDescriptor

const file_pbentity_video_playlist_relations_proto_rawDesc = "" +
	"\n" +
	"'pbentity/video_playlist_relations.proto\x12\bpbentity\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf4\x01\n" +
	"\x16VideoPlaylistRelations\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x1e\n" +
	"\n" +
	"PlaylistId\x18\x02 \x01(\rR\n" +
	"PlaylistId\x12\x18\n" +
	"\aVideoId\x18\x03 \x01(\rR\aVideoId\x12\x1c\n" +
	"\tSortOrder\x18\x04 \x01(\rR\tSortOrder\x128\n" +
	"\tCreatedAt\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tCreatedAt\x128\n" +
	"\tUpdatedAt\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tUpdatedAtB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_video_playlist_relations_proto_rawDescOnce sync.Once
	file_pbentity_video_playlist_relations_proto_rawDescData []byte
)

func file_pbentity_video_playlist_relations_proto_rawDescGZIP() []byte {
	file_pbentity_video_playlist_relations_proto_rawDescOnce.Do(func() {
		file_pbentity_video_playlist_relations_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_video_playlist_relations_proto_rawDesc), len(file_pbentity_video_playlist_relations_proto_rawDesc)))
	})
	return file_pbentity_video_playlist_relations_proto_rawDescData
}

var file_pbentity_video_playlist_relations_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_video_playlist_relations_proto_goTypes = []any{
	(*VideoPlaylistRelations)(nil), // 0: pbentity.VideoPlaylistRelations
	(*timestamppb.Timestamp)(nil),  // 1: google.protobuf.Timestamp
}
var file_pbentity_video_playlist_relations_proto_depIdxs = []int32{
	1, // 0: pbentity.VideoPlaylistRelations.CreatedAt:type_name -> google.protobuf.Timestamp
	1, // 1: pbentity.VideoPlaylistRelations.UpdatedAt:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pbentity_video_playlist_relations_proto_init() }
func file_pbentity_video_playlist_relations_proto_init() {
	if File_pbentity_video_playlist_relations_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_video_playlist_relations_proto_rawDesc), len(file_pbentity_video_playlist_relations_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_video_playlist_relations_proto_goTypes,
		DependencyIndexes: file_pbentity_video_playlist_relations_proto_depIdxs,
		MessageInfos:      file_pbentity_video_playlist_relations_proto_msgTypes,
	}.Build()
	File_pbentity_video_playlist_relations_proto = out.File
	file_pbentity_video_playlist_relations_proto_goTypes = nil
	file_pbentity_video_playlist_relations_proto_depIdxs = nil
}
