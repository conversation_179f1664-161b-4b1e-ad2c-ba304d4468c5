// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/calendar_events.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CalendarEvents struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int64                  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                                           // 主键ID
	EventType      string                 `protobuf:"bytes,2,opt,name=EventType,proto3" json:"EventType,omitempty" dc:"事件类型：HARI_BESAR-重大节日，LIBUR_NASIONAL-国定假日，PUASA-斋戒"` // 事件类型：HARI_BESAR-重大节日，LIBUR_NASIONAL-国定假日，PUASA-斋戒
	Title          string                 `protobuf:"bytes,3,opt,name=Title,proto3" json:"Title,omitempty" dc:"事件标题"`                                                      // 事件标题
	Description    string                 `protobuf:"bytes,4,opt,name=Description,proto3" json:"Description,omitempty" dc:"事件描述"`                                          // 事件描述
	GregorianYear  int32                  `protobuf:"varint,5,opt,name=GregorianYear,proto3" json:"GregorianYear,omitempty" dc:"公历年"`                                      // 公历年
	GregorianMonth int32                  `protobuf:"varint,6,opt,name=GregorianMonth,proto3" json:"GregorianMonth,omitempty" dc:"公历月"`                                    // 公历月
	GregorianDay   int32                  `protobuf:"varint,7,opt,name=GregorianDay,proto3" json:"GregorianDay,omitempty" dc:"公历日"`                                        // 公历日
	JumpUrl        string                 `protobuf:"bytes,8,opt,name=JumpUrl,proto3" json:"JumpUrl,omitempty" dc:"点击跳转链接"`                                                // 点击跳转链接
	DataSource     string                 `protobuf:"bytes,9,opt,name=DataSource,proto3" json:"DataSource,omitempty" dc:"数据来源：MANUAL-人工录入，CRAWLER-爬虫获取"`                   // 数据来源：MANUAL-人工录入，CRAWLER-爬虫获取
	IsActive       int32                  `protobuf:"varint,10,opt,name=IsActive,proto3" json:"IsActive,omitempty" dc:"是否启用：0-禁用，1-启用"`                                    // 是否启用：0-禁用，1-启用
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty" dc:"创建时间"`                                             // 创建时间
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" dc:"更新时间"`                                             // 更新时间
}

func (x *CalendarEvents) Reset() {
	*x = CalendarEvents{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_calendar_events_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarEvents) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarEvents) ProtoMessage() {}

func (x *CalendarEvents) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_calendar_events_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarEvents.ProtoReflect.Descriptor instead.
func (*CalendarEvents) Descriptor() ([]byte, []int) {
	return file_pbentity_calendar_events_proto_rawDescGZIP(), []int{0}
}

func (x *CalendarEvents) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CalendarEvents) GetEventType() string {
	if x != nil {
		return x.EventType
	}
	return ""
}

func (x *CalendarEvents) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CalendarEvents) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CalendarEvents) GetGregorianYear() int32 {
	if x != nil {
		return x.GregorianYear
	}
	return 0
}

func (x *CalendarEvents) GetGregorianMonth() int32 {
	if x != nil {
		return x.GregorianMonth
	}
	return 0
}

func (x *CalendarEvents) GetGregorianDay() int32 {
	if x != nil {
		return x.GregorianDay
	}
	return 0
}

func (x *CalendarEvents) GetJumpUrl() string {
	if x != nil {
		return x.JumpUrl
	}
	return ""
}

func (x *CalendarEvents) GetDataSource() string {
	if x != nil {
		return x.DataSource
	}
	return ""
}

func (x *CalendarEvents) GetIsActive() int32 {
	if x != nil {
		return x.IsActive
	}
	return 0
}

func (x *CalendarEvents) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CalendarEvents) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_pbentity_calendar_events_proto protoreflect.FileDescriptor

var file_pbentity_calendar_events_proto_rawDesc = []byte{
	0x0a, 0x1e, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x63, 0x61, 0x6c, 0x65, 0x6e,
	0x64, 0x61, 0x72, 0x5f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb2, 0x03, 0x0a, 0x0e,
	0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x0e,
	0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x49, 0x64, 0x12, 0x1c,
	0x0a, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x69, 0x74,
	0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x24, 0x0a, 0x0d, 0x47, 0x72, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x61,
	0x6e, 0x59, 0x65, 0x61, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x47, 0x72, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x61, 0x6e, 0x59, 0x65, 0x61, 0x72, 0x12, 0x26, 0x0a, 0x0e, 0x47, 0x72,
	0x65, 0x67, 0x6f, 0x72, 0x69, 0x61, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0e, 0x47, 0x72, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x61, 0x6e, 0x4d, 0x6f, 0x6e,
	0x74, 0x68, 0x12, 0x22, 0x0a, 0x0c, 0x47, 0x72, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x61, 0x6e, 0x44,
	0x61, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x47, 0x72, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x61, 0x6e, 0x44, 0x61, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x4a, 0x75, 0x6d, 0x70, 0x55, 0x72,
	0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x4a, 0x75, 0x6d, 0x70, 0x55, 0x72, 0x6c,
	0x12, 0x1e, 0x0a, 0x0a, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x44, 0x61, 0x74, 0x61, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x49, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x49, 0x73, 0x41, 0x63, 0x74, 0x69, 0x76, 0x65, 0x12, 0x38, 0x0a, 0x09,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_calendar_events_proto_rawDescOnce sync.Once
	file_pbentity_calendar_events_proto_rawDescData = file_pbentity_calendar_events_proto_rawDesc
)

func file_pbentity_calendar_events_proto_rawDescGZIP() []byte {
	file_pbentity_calendar_events_proto_rawDescOnce.Do(func() {
		file_pbentity_calendar_events_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_calendar_events_proto_rawDescData)
	})
	return file_pbentity_calendar_events_proto_rawDescData
}

var file_pbentity_calendar_events_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_calendar_events_proto_goTypes = []interface{}{
	(*CalendarEvents)(nil),        // 0: pbentity.CalendarEvents
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_pbentity_calendar_events_proto_depIdxs = []int32{
	1, // 0: pbentity.CalendarEvents.CreatedAt:type_name -> google.protobuf.Timestamp
	1, // 1: pbentity.CalendarEvents.UpdatedAt:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pbentity_calendar_events_proto_init() }
func file_pbentity_calendar_events_proto_init() {
	if File_pbentity_calendar_events_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_calendar_events_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarEvents); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_calendar_events_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_calendar_events_proto_goTypes,
		DependencyIndexes: file_pbentity_calendar_events_proto_depIdxs,
		MessageInfos:      file_pbentity_calendar_events_proto_msgTypes,
	}.Build()
	File_pbentity_calendar_events_proto = out.File
	file_pbentity_calendar_events_proto_rawDesc = nil
	file_pbentity_calendar_events_proto_goTypes = nil
	file_pbentity_calendar_events_proto_depIdxs = nil
}
