// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/news_category.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NewsCategory struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                             //
	ParentId      uint32                 `protobuf:"varint,2,opt,name=ParentId,proto3" json:"ParentId,omitempty" dc:"上级id，0表示顶级"` // 上级id，0表示顶级
	IsZh          uint32                 `protobuf:"varint,3,opt,name=IsZh,proto3" json:"IsZh,omitempty" dc:"是否中文，0-否，1-是"`       // 是否中文，0-否，1-是
	IsEn          uint32                 `protobuf:"varint,4,opt,name=IsEn,proto3" json:"IsEn,omitempty" dc:"是否英文，0-否，1-是"`       // 是否英文，0-否，1-是
	IsId          uint32                 `protobuf:"varint,5,opt,name=IsId,proto3" json:"IsId,omitempty" dc:"是否印尼文，0-否，1-是"`      // 是否印尼文，0-否，1-是
	Status        uint32                 `protobuf:"varint,6,opt,name=Status,proto3" json:"Status,omitempty" dc:"状态，1启用，0关闭"`     // 状态，1启用，0关闭
	Sort          uint32                 `protobuf:"varint,7,opt,name=Sort,proto3" json:"Sort,omitempty" dc:"排序，数字越小，排序越靠前"`      // 排序，数字越小，排序越靠前
	AdminId       uint32                 `protobuf:"varint,8,opt,name=AdminId,proto3" json:"AdminId,omitempty" dc:"分类负责人id"`      // 分类负责人id
	CoverImgs     string                 `protobuf:"bytes,9,opt,name=CoverImgs,proto3" json:"CoverImgs,omitempty" dc:"封面图"`       // 封面图
	Remark        string                 `protobuf:"bytes,10,opt,name=Remark,proto3" json:"Remark,omitempty" dc:"备注"`             // 备注
	Creater       uint32                 `protobuf:"varint,11,opt,name=Creater,proto3" json:"Creater,omitempty" dc:"创建者id"`       // 创建者id
	CreateName    string                 `protobuf:"bytes,12,opt,name=CreateName,proto3" json:"CreateName,omitempty" dc:"创建者"`    // 创建者
	CreateTime    int64                  `protobuf:"varint,13,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间"`  // 创建时间
	UpdateTime    int64                  `protobuf:"varint,14,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"修改时间"`  // 修改时间
	DeleteTime    int64                  `protobuf:"varint,15,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty" dc:"删除时间"`  // 删除时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsCategory) Reset() {
	*x = NewsCategory{}
	mi := &file_pbentity_news_category_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCategory) ProtoMessage() {}

func (x *NewsCategory) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_news_category_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCategory.ProtoReflect.Descriptor instead.
func (*NewsCategory) Descriptor() ([]byte, []int) {
	return file_pbentity_news_category_proto_rawDescGZIP(), []int{0}
}

func (x *NewsCategory) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NewsCategory) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *NewsCategory) GetIsZh() uint32 {
	if x != nil {
		return x.IsZh
	}
	return 0
}

func (x *NewsCategory) GetIsEn() uint32 {
	if x != nil {
		return x.IsEn
	}
	return 0
}

func (x *NewsCategory) GetIsId() uint32 {
	if x != nil {
		return x.IsId
	}
	return 0
}

func (x *NewsCategory) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *NewsCategory) GetSort() uint32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *NewsCategory) GetAdminId() uint32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *NewsCategory) GetCoverImgs() string {
	if x != nil {
		return x.CoverImgs
	}
	return ""
}

func (x *NewsCategory) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *NewsCategory) GetCreater() uint32 {
	if x != nil {
		return x.Creater
	}
	return 0
}

func (x *NewsCategory) GetCreateName() string {
	if x != nil {
		return x.CreateName
	}
	return ""
}

func (x *NewsCategory) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *NewsCategory) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *NewsCategory) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

var File_pbentity_news_category_proto protoreflect.FileDescriptor

const file_pbentity_news_category_proto_rawDesc = "" +
	"\n" +
	"\x1cpbentity/news_category.proto\x12\bpbentity\"\x8c\x03\n" +
	"\fNewsCategory\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x1a\n" +
	"\bParentId\x18\x02 \x01(\rR\bParentId\x12\x12\n" +
	"\x04IsZh\x18\x03 \x01(\rR\x04IsZh\x12\x12\n" +
	"\x04IsEn\x18\x04 \x01(\rR\x04IsEn\x12\x12\n" +
	"\x04IsId\x18\x05 \x01(\rR\x04IsId\x12\x16\n" +
	"\x06Status\x18\x06 \x01(\rR\x06Status\x12\x12\n" +
	"\x04Sort\x18\a \x01(\rR\x04Sort\x12\x18\n" +
	"\aAdminId\x18\b \x01(\rR\aAdminId\x12\x1c\n" +
	"\tCoverImgs\x18\t \x01(\tR\tCoverImgs\x12\x16\n" +
	"\x06Remark\x18\n" +
	" \x01(\tR\x06Remark\x12\x18\n" +
	"\aCreater\x18\v \x01(\rR\aCreater\x12\x1e\n" +
	"\n" +
	"CreateName\x18\f \x01(\tR\n" +
	"CreateName\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\r \x01(\x03R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\x0e \x01(\x03R\n" +
	"UpdateTime\x12\x1e\n" +
	"\n" +
	"DeleteTime\x18\x0f \x01(\x03R\n" +
	"DeleteTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_news_category_proto_rawDescOnce sync.Once
	file_pbentity_news_category_proto_rawDescData []byte
)

func file_pbentity_news_category_proto_rawDescGZIP() []byte {
	file_pbentity_news_category_proto_rawDescOnce.Do(func() {
		file_pbentity_news_category_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_news_category_proto_rawDesc), len(file_pbentity_news_category_proto_rawDesc)))
	})
	return file_pbentity_news_category_proto_rawDescData
}

var file_pbentity_news_category_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_news_category_proto_goTypes = []any{
	(*NewsCategory)(nil), // 0: pbentity.NewsCategory
}
var file_pbentity_news_category_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_news_category_proto_init() }
func file_pbentity_news_category_proto_init() {
	if File_pbentity_news_category_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_news_category_proto_rawDesc), len(file_pbentity_news_category_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_news_category_proto_goTypes,
		DependencyIndexes: file_pbentity_news_category_proto_depIdxs,
		MessageInfos:      file_pbentity_news_category_proto_msgTypes,
	}.Build()
	File_pbentity_news_category_proto = out.File
	file_pbentity_news_category_proto_goTypes = nil
	file_pbentity_news_category_proto_depIdxs = nil
}
