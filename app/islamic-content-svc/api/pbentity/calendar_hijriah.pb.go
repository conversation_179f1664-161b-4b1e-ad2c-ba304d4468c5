// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/calendar_hijriah.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CalendarHijriah struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id             int64                  `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                                               // 主键ID
	GregorianYear  int32                  `protobuf:"varint,2,opt,name=GregorianYear,proto3" json:"GregorianYear,omitempty" dc:"公历年"`                                          // 公历年
	GregorianMonth int32                  `protobuf:"varint,3,opt,name=GregorianMonth,proto3" json:"GregorianMonth,omitempty" dc:"公历月"`                                        // 公历月
	GregorianDay   int32                  `protobuf:"varint,4,opt,name=GregorianDay,proto3" json:"GregorianDay,omitempty" dc:"公历日"`                                            // 公历日
	HijriahYear    int32                  `protobuf:"varint,5,opt,name=HijriahYear,proto3" json:"HijriahYear,omitempty" dc:"Hijriah年"`                                         // Hijriah年
	HijriahMonth   int32                  `protobuf:"varint,6,opt,name=HijriahMonth,proto3" json:"HijriahMonth,omitempty" dc:"Hijriah月"`                                       // Hijriah月
	HijriahDay     int32                  `protobuf:"varint,7,opt,name=HijriahDay,proto3" json:"HijriahDay,omitempty" dc:"Hijriah日"`                                           // Hijriah日
	MethodCode     string                 `protobuf:"bytes,8,opt,name=MethodCode,proto3" json:"MethodCode,omitempty" dc:"计算方法代码，如：LFNU, UMMUL_QURA"`                           // 计算方法代码，如：LFNU, UMMUL_QURA
	Weekday        int32                  `protobuf:"varint,9,opt,name=Weekday,proto3" json:"Weekday,omitempty" dc:"星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)"`             // 星期：0-6 (0=Sunday, 1=Monday, ..., 6=Saturday)
	Pasaran        int32                  `protobuf:"varint,10,opt,name=Pasaran,proto3" json:"Pasaran,omitempty" dc:"Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)"` // Pasaran：0-4 (0=Kliwon, 1=Legi, 2=Pahing, 3=Pon, 4=Wage)
	CreatedAt      *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty" dc:"创建时间"`                                                 // 创建时间
	UpdatedAt      *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" dc:"更新时间"`                                                 // 更新时间
}

func (x *CalendarHijriah) Reset() {
	*x = CalendarHijriah{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_calendar_hijriah_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CalendarHijriah) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CalendarHijriah) ProtoMessage() {}

func (x *CalendarHijriah) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_calendar_hijriah_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CalendarHijriah.ProtoReflect.Descriptor instead.
func (*CalendarHijriah) Descriptor() ([]byte, []int) {
	return file_pbentity_calendar_hijriah_proto_rawDescGZIP(), []int{0}
}

func (x *CalendarHijriah) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CalendarHijriah) GetGregorianYear() int32 {
	if x != nil {
		return x.GregorianYear
	}
	return 0
}

func (x *CalendarHijriah) GetGregorianMonth() int32 {
	if x != nil {
		return x.GregorianMonth
	}
	return 0
}

func (x *CalendarHijriah) GetGregorianDay() int32 {
	if x != nil {
		return x.GregorianDay
	}
	return 0
}

func (x *CalendarHijriah) GetHijriahYear() int32 {
	if x != nil {
		return x.HijriahYear
	}
	return 0
}

func (x *CalendarHijriah) GetHijriahMonth() int32 {
	if x != nil {
		return x.HijriahMonth
	}
	return 0
}

func (x *CalendarHijriah) GetHijriahDay() int32 {
	if x != nil {
		return x.HijriahDay
	}
	return 0
}

func (x *CalendarHijriah) GetMethodCode() string {
	if x != nil {
		return x.MethodCode
	}
	return ""
}

func (x *CalendarHijriah) GetWeekday() int32 {
	if x != nil {
		return x.Weekday
	}
	return 0
}

func (x *CalendarHijriah) GetPasaran() int32 {
	if x != nil {
		return x.Pasaran
	}
	return 0
}

func (x *CalendarHijriah) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *CalendarHijriah) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_pbentity_calendar_hijriah_proto protoreflect.FileDescriptor

var file_pbentity_calendar_hijriah_proto_rawDesc = []byte{
	0x0a, 0x1f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x63, 0x61, 0x6c, 0x65, 0x6e,
	0x64, 0x61, 0x72, 0x5f, 0x68, 0x69, 0x6a, 0x72, 0x69, 0x61, 0x68, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc1, 0x03, 0x0a,
	0x0f, 0x43, 0x61, 0x6c, 0x65, 0x6e, 0x64, 0x61, 0x72, 0x48, 0x69, 0x6a, 0x72, 0x69, 0x61, 0x68,
	0x12, 0x0e, 0x0a, 0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x49, 0x64,
	0x12, 0x24, 0x0a, 0x0d, 0x47, 0x72, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x61, 0x6e, 0x59, 0x65, 0x61,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0d, 0x47, 0x72, 0x65, 0x67, 0x6f, 0x72, 0x69,
	0x61, 0x6e, 0x59, 0x65, 0x61, 0x72, 0x12, 0x26, 0x0a, 0x0e, 0x47, 0x72, 0x65, 0x67, 0x6f, 0x72,
	0x69, 0x61, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0e,
	0x47, 0x72, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x61, 0x6e, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x22,
	0x0a, 0x0c, 0x47, 0x72, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x61, 0x6e, 0x44, 0x61, 0x79, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x47, 0x72, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x61, 0x6e, 0x44,
	0x61, 0x79, 0x12, 0x20, 0x0a, 0x0b, 0x48, 0x69, 0x6a, 0x72, 0x69, 0x61, 0x68, 0x59, 0x65, 0x61,
	0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0b, 0x48, 0x69, 0x6a, 0x72, 0x69, 0x61, 0x68,
	0x59, 0x65, 0x61, 0x72, 0x12, 0x22, 0x0a, 0x0c, 0x48, 0x69, 0x6a, 0x72, 0x69, 0x61, 0x68, 0x4d,
	0x6f, 0x6e, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x48, 0x69, 0x6a, 0x72,
	0x69, 0x61, 0x68, 0x4d, 0x6f, 0x6e, 0x74, 0x68, 0x12, 0x1e, 0x0a, 0x0a, 0x48, 0x69, 0x6a, 0x72,
	0x69, 0x61, 0x68, 0x44, 0x61, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x48, 0x69,
	0x6a, 0x72, 0x69, 0x61, 0x68, 0x44, 0x61, 0x79, 0x12, 0x1e, 0x0a, 0x0a, 0x4d, 0x65, 0x74, 0x68,
	0x6f, 0x64, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x4d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x57, 0x65, 0x65, 0x6b,
	0x64, 0x61, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x57, 0x65, 0x65, 0x6b, 0x64,
	0x61, 0x79, 0x12, 0x18, 0x0a, 0x07, 0x50, 0x61, 0x73, 0x61, 0x72, 0x61, 0x6e, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x07, 0x50, 0x61, 0x73, 0x61, 0x72, 0x61, 0x6e, 0x12, 0x38, 0x0a, 0x09,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x41, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63, 0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e,
	0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pbentity_calendar_hijriah_proto_rawDescOnce sync.Once
	file_pbentity_calendar_hijriah_proto_rawDescData = file_pbentity_calendar_hijriah_proto_rawDesc
)

func file_pbentity_calendar_hijriah_proto_rawDescGZIP() []byte {
	file_pbentity_calendar_hijriah_proto_rawDescOnce.Do(func() {
		file_pbentity_calendar_hijriah_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_calendar_hijriah_proto_rawDescData)
	})
	return file_pbentity_calendar_hijriah_proto_rawDescData
}

var file_pbentity_calendar_hijriah_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_calendar_hijriah_proto_goTypes = []interface{}{
	(*CalendarHijriah)(nil),       // 0: pbentity.CalendarHijriah
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_pbentity_calendar_hijriah_proto_depIdxs = []int32{
	1, // 0: pbentity.CalendarHijriah.CreatedAt:type_name -> google.protobuf.Timestamp
	1, // 1: pbentity.CalendarHijriah.UpdatedAt:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pbentity_calendar_hijriah_proto_init() }
func file_pbentity_calendar_hijriah_proto_init() {
	if File_pbentity_calendar_hijriah_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_calendar_hijriah_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CalendarHijriah); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_calendar_hijriah_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_calendar_hijriah_proto_goTypes,
		DependencyIndexes: file_pbentity_calendar_hijriah_proto_depIdxs,
		MessageInfos:      file_pbentity_calendar_hijriah_proto_msgTypes,
	}.Build()
	File_pbentity_calendar_hijriah_proto = out.File
	file_pbentity_calendar_hijriah_proto_rawDesc = nil
	file_pbentity_calendar_hijriah_proto_goTypes = nil
	file_pbentity_calendar_hijriah_proto_depIdxs = nil
}
