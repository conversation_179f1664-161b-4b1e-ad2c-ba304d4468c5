// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: pbentity/video_play_history.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VideoPlayHistory struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                             // 主键ID
	UserId        uint64                 `protobuf:"varint,2,opt,name=UserId,proto3" json:"UserId,omitempty" dc:"用户ID"`                     // 用户ID
	VideoId       uint32                 `protobuf:"varint,3,opt,name=VideoId,proto3" json:"VideoId,omitempty" dc:"视频ID"`                   // 视频ID
	PlayPosition  uint32                 `protobuf:"varint,4,opt,name=PlayPosition,proto3" json:"PlayPosition,omitempty" dc:"播放位置(秒)"`      // 播放位置(秒)
	PlayDuration  uint32                 `protobuf:"varint,5,opt,name=PlayDuration,proto3" json:"PlayDuration,omitempty" dc:"本次播放时长(秒)"`    // 本次播放时长(秒)
	IsCompleted   uint32                 `protobuf:"varint,6,opt,name=IsCompleted,proto3" json:"IsCompleted,omitempty" dc:"是否播放完成，0-否，1-是"` // 是否播放完成，0-否，1-是
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty" dc:"播放时间"`                // 播放时间
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" dc:"更新时间"`                // 更新时间
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlayHistory) Reset() {
	*x = VideoPlayHistory{}
	mi := &file_pbentity_video_play_history_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlayHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayHistory) ProtoMessage() {}

func (x *VideoPlayHistory) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_video_play_history_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayHistory.ProtoReflect.Descriptor instead.
func (*VideoPlayHistory) Descriptor() ([]byte, []int) {
	return file_pbentity_video_play_history_proto_rawDescGZIP(), []int{0}
}

func (x *VideoPlayHistory) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VideoPlayHistory) GetUserId() uint64 {
	if x != nil {
		return x.UserId
	}
	return 0
}

func (x *VideoPlayHistory) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *VideoPlayHistory) GetPlayPosition() uint32 {
	if x != nil {
		return x.PlayPosition
	}
	return 0
}

func (x *VideoPlayHistory) GetPlayDuration() uint32 {
	if x != nil {
		return x.PlayDuration
	}
	return 0
}

func (x *VideoPlayHistory) GetIsCompleted() uint32 {
	if x != nil {
		return x.IsCompleted
	}
	return 0
}

func (x *VideoPlayHistory) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *VideoPlayHistory) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_pbentity_video_play_history_proto protoreflect.FileDescriptor

const file_pbentity_video_play_history_proto_rawDesc = "" +
	"\n" +
	"!pbentity/video_play_history.proto\x12\bpbentity\x1a\x1fgoogle/protobuf/timestamp.proto\"\xb2\x02\n" +
	"\x10VideoPlayHistory\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\x04R\x02Id\x12\x16\n" +
	"\x06UserId\x18\x02 \x01(\x04R\x06UserId\x12\x18\n" +
	"\aVideoId\x18\x03 \x01(\rR\aVideoId\x12\"\n" +
	"\fPlayPosition\x18\x04 \x01(\rR\fPlayPosition\x12\"\n" +
	"\fPlayDuration\x18\x05 \x01(\rR\fPlayDuration\x12 \n" +
	"\vIsCompleted\x18\x06 \x01(\rR\vIsCompleted\x128\n" +
	"\tCreatedAt\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tCreatedAt\x128\n" +
	"\tUpdatedAt\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\tUpdatedAtB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_video_play_history_proto_rawDescOnce sync.Once
	file_pbentity_video_play_history_proto_rawDescData []byte
)

func file_pbentity_video_play_history_proto_rawDescGZIP() []byte {
	file_pbentity_video_play_history_proto_rawDescOnce.Do(func() {
		file_pbentity_video_play_history_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_video_play_history_proto_rawDesc), len(file_pbentity_video_play_history_proto_rawDesc)))
	})
	return file_pbentity_video_play_history_proto_rawDescData
}

var file_pbentity_video_play_history_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_video_play_history_proto_goTypes = []any{
	(*VideoPlayHistory)(nil),      // 0: pbentity.VideoPlayHistory
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_pbentity_video_play_history_proto_depIdxs = []int32{
	1, // 0: pbentity.VideoPlayHistory.CreatedAt:type_name -> google.protobuf.Timestamp
	1, // 1: pbentity.VideoPlayHistory.UpdatedAt:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pbentity_video_play_history_proto_init() }
func file_pbentity_video_play_history_proto_init() {
	if File_pbentity_video_play_history_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_video_play_history_proto_rawDesc), len(file_pbentity_video_play_history_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_video_play_history_proto_goTypes,
		DependencyIndexes: file_pbentity_video_play_history_proto_depIdxs,
		MessageInfos:      file_pbentity_video_play_history_proto_msgTypes,
	}.Build()
	File_pbentity_video_play_history_proto = out.File
	file_pbentity_video_play_history_proto_goTypes = nil
	file_pbentity_video_play_history_proto_depIdxs = nil
}
