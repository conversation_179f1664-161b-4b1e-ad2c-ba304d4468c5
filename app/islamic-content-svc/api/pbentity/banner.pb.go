// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: pbentity/banner.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Banner struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty" dc:"主键ID"`                                    // 主键ID
	LanguageId  uint32                 `protobuf:"varint,2,opt,name=LanguageId,proto3" json:"LanguageId,omitempty" dc:"语言ID: 0-中文, 1-英文, 2-印尼语"` // 语言ID: 0-中文, 1-英文, 2-印尼语
	Title       string                 `protobuf:"bytes,3,opt,name=Title,proto3" json:"Title,omitempty" dc:"广告标题"`                               // 广告标题
	Description string                 `protobuf:"bytes,4,opt,name=Description,proto3" json:"Description,omitempty" dc:"广告描述"`                   // 广告描述
	ImageUrl    string                 `protobuf:"bytes,5,opt,name=ImageUrl,proto3" json:"ImageUrl,omitempty" dc:"广告图片URL"`                      // 广告图片URL
	LinkUrl     string                 `protobuf:"bytes,6,opt,name=LinkUrl,proto3" json:"LinkUrl,omitempty" dc:"跳转链接URL"`                        // 跳转链接URL
	SortOrder   uint32                 `protobuf:"varint,7,opt,name=SortOrder,proto3" json:"SortOrder,omitempty" dc:"排序权重，数字越小越靠前"`              // 排序权重，数字越小越靠前
	Status      uint32                 `protobuf:"varint,8,opt,name=Status,proto3" json:"Status,omitempty" dc:"状态: 0-禁用, 1-启用"`                  // 状态: 0-禁用, 1-启用
	StartTime   uint64                 `protobuf:"varint,9,opt,name=StartTime,proto3" json:"StartTime,omitempty" dc:"开始时间戳(毫秒)"`                 // 开始时间戳(毫秒)
	EndTime     uint64                 `protobuf:"varint,10,opt,name=EndTime,proto3" json:"EndTime,omitempty" dc:"结束时间戳(毫秒)"`                    // 结束时间戳(毫秒)
	AdminId     uint32                 `protobuf:"varint,11,opt,name=AdminId,proto3" json:"AdminId,omitempty" dc:"创建管理员ID"`                      // 创建管理员ID
	CreatedAt   *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=CreatedAt,proto3" json:"CreatedAt,omitempty" dc:"创建时间"`                      // 创建时间
	UpdatedAt   *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=UpdatedAt,proto3" json:"UpdatedAt,omitempty" dc:"更新时间"`                      // 更新时间
}

func (x *Banner) Reset() {
	*x = Banner{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pbentity_banner_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Banner) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Banner) ProtoMessage() {}

func (x *Banner) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_banner_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Banner.ProtoReflect.Descriptor instead.
func (*Banner) Descriptor() ([]byte, []int) {
	return file_pbentity_banner_proto_rawDescGZIP(), []int{0}
}

func (x *Banner) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Banner) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *Banner) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Banner) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Banner) GetImageUrl() string {
	if x != nil {
		return x.ImageUrl
	}
	return ""
}

func (x *Banner) GetLinkUrl() string {
	if x != nil {
		return x.LinkUrl
	}
	return ""
}

func (x *Banner) GetSortOrder() uint32 {
	if x != nil {
		return x.SortOrder
	}
	return 0
}

func (x *Banner) GetStatus() uint32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Banner) GetStartTime() uint64 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *Banner) GetEndTime() uint64 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *Banner) GetAdminId() uint32 {
	if x != nil {
		return x.AdminId
	}
	return 0
}

func (x *Banner) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *Banner) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

var File_pbentity_banner_proto protoreflect.FileDescriptor

var file_pbentity_banner_proto_rawDesc = []byte{
	0x0a, 0x15, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x2f, 0x62, 0x61, 0x6e, 0x6e, 0x65,
	0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xa2, 0x03, 0x0a, 0x06, 0x42, 0x61, 0x6e, 0x6e, 0x65, 0x72, 0x12, 0x0e, 0x0a,
	0x02, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x02, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x0a, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0d, 0x52, 0x0a, 0x4c, 0x61, 0x6e, 0x67, 0x75, 0x61, 0x67, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x54, 0x69, 0x74, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x54, 0x69,
	0x74, 0x6c, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x55, 0x72,
	0x6c, 0x12, 0x18, 0x0a, 0x07, 0x4c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x4c, 0x69, 0x6e, 0x6b, 0x55, 0x72, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x53,
	0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x09,
	0x53, 0x6f, 0x72, 0x74, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x1c, 0x0a, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x53, 0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x07, 0x45, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x49, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x49, 0x64, 0x12, 0x38, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74,
	0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x12, 0x38, 0x0a,
	0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x09, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x30, 0x5a, 0x2e, 0x68, 0x61, 0x6c, 0x61, 0x6c,
	0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x69, 0x73, 0x6c, 0x61, 0x6d, 0x69, 0x63,
	0x2d, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x70, 0x62, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_pbentity_banner_proto_rawDescOnce sync.Once
	file_pbentity_banner_proto_rawDescData = file_pbentity_banner_proto_rawDesc
)

func file_pbentity_banner_proto_rawDescGZIP() []byte {
	file_pbentity_banner_proto_rawDescOnce.Do(func() {
		file_pbentity_banner_proto_rawDescData = protoimpl.X.CompressGZIP(file_pbentity_banner_proto_rawDescData)
	})
	return file_pbentity_banner_proto_rawDescData
}

var file_pbentity_banner_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_banner_proto_goTypes = []interface{}{
	(*Banner)(nil),                // 0: pbentity.Banner
	(*timestamppb.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_pbentity_banner_proto_depIdxs = []int32{
	1, // 0: pbentity.Banner.CreatedAt:type_name -> google.protobuf.Timestamp
	1, // 1: pbentity.Banner.UpdatedAt:type_name -> google.protobuf.Timestamp
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_pbentity_banner_proto_init() }
func file_pbentity_banner_proto_init() {
	if File_pbentity_banner_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_pbentity_banner_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Banner); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pbentity_banner_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_banner_proto_goTypes,
		DependencyIndexes: file_pbentity_banner_proto_depIdxs,
		MessageInfos:      file_pbentity_banner_proto_msgTypes,
	}.Build()
	File_pbentity_banner_proto = out.File
	file_pbentity_banner_proto_rawDesc = nil
	file_pbentity_banner_proto_goTypes = nil
	file_pbentity_banner_proto_depIdxs = nil
}
