// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: islamic/v1/wisdow.proto

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	WisdomService_WisdomCateList_FullMethodName = "/islamic.v1.WisdomService/WisdomCateList"
	WisdomService_WisdomList_FullMethodName     = "/islamic.v1.WisdomService/WisdomList"
	WisdomService_WisdomOne_FullMethodName      = "/islamic.v1.WisdomService/WisdomOne"
)

// WisdomServiceClient is the client API for WisdomService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 名言服务定义
type WisdomServiceClient interface {
	// 名言分类列表
	WisdomCateList(ctx context.Context, in *WisdomCateReq, opts ...grpc.CallOption) (*WisdomCateRes, error)
	// 名言列表
	WisdomList(ctx context.Context, in *WisdomListReq, opts ...grpc.CallOption) (*WisdomListRes, error)
	// 名言详情
	WisdomOne(ctx context.Context, in *WisdomOneReq, opts ...grpc.CallOption) (*WisdomOneRes, error)
}

type wisdomServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewWisdomServiceClient(cc grpc.ClientConnInterface) WisdomServiceClient {
	return &wisdomServiceClient{cc}
}

func (c *wisdomServiceClient) WisdomCateList(ctx context.Context, in *WisdomCateReq, opts ...grpc.CallOption) (*WisdomCateRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WisdomCateRes)
	err := c.cc.Invoke(ctx, WisdomService_WisdomCateList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wisdomServiceClient) WisdomList(ctx context.Context, in *WisdomListReq, opts ...grpc.CallOption) (*WisdomListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WisdomListRes)
	err := c.cc.Invoke(ctx, WisdomService_WisdomList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *wisdomServiceClient) WisdomOne(ctx context.Context, in *WisdomOneReq, opts ...grpc.CallOption) (*WisdomOneRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(WisdomOneRes)
	err := c.cc.Invoke(ctx, WisdomService_WisdomOne_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WisdomServiceServer is the server API for WisdomService service.
// All implementations must embed UnimplementedWisdomServiceServer
// for forward compatibility.
//
// 名言服务定义
type WisdomServiceServer interface {
	// 名言分类列表
	WisdomCateList(context.Context, *WisdomCateReq) (*WisdomCateRes, error)
	// 名言列表
	WisdomList(context.Context, *WisdomListReq) (*WisdomListRes, error)
	// 名言详情
	WisdomOne(context.Context, *WisdomOneReq) (*WisdomOneRes, error)
	mustEmbedUnimplementedWisdomServiceServer()
}

// UnimplementedWisdomServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedWisdomServiceServer struct{}

func (UnimplementedWisdomServiceServer) WisdomCateList(context.Context, *WisdomCateReq) (*WisdomCateRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WisdomCateList not implemented")
}
func (UnimplementedWisdomServiceServer) WisdomList(context.Context, *WisdomListReq) (*WisdomListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WisdomList not implemented")
}
func (UnimplementedWisdomServiceServer) WisdomOne(context.Context, *WisdomOneReq) (*WisdomOneRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WisdomOne not implemented")
}
func (UnimplementedWisdomServiceServer) mustEmbedUnimplementedWisdomServiceServer() {}
func (UnimplementedWisdomServiceServer) testEmbeddedByValue()                       {}

// UnsafeWisdomServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WisdomServiceServer will
// result in compilation errors.
type UnsafeWisdomServiceServer interface {
	mustEmbedUnimplementedWisdomServiceServer()
}

func RegisterWisdomServiceServer(s grpc.ServiceRegistrar, srv WisdomServiceServer) {
	// If the following call pancis, it indicates UnimplementedWisdomServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&WisdomService_ServiceDesc, srv)
}

func _WisdomService_WisdomCateList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WisdomCateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WisdomServiceServer).WisdomCateList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WisdomService_WisdomCateList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WisdomServiceServer).WisdomCateList(ctx, req.(*WisdomCateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WisdomService_WisdomList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WisdomListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WisdomServiceServer).WisdomList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WisdomService_WisdomList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WisdomServiceServer).WisdomList(ctx, req.(*WisdomListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _WisdomService_WisdomOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WisdomOneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WisdomServiceServer).WisdomOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: WisdomService_WisdomOne_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WisdomServiceServer).WisdomOne(ctx, req.(*WisdomOneReq))
	}
	return interceptor(ctx, in, info, handler)
}

// WisdomService_ServiceDesc is the grpc.ServiceDesc for WisdomService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var WisdomService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.WisdomService",
	HandlerType: (*WisdomServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "WisdomCateList",
			Handler:    _WisdomService_WisdomCateList_Handler,
		},
		{
			MethodName: "WisdomList",
			Handler:    _WisdomService_WisdomList_Handler,
		},
		{
			MethodName: "WisdomOne",
			Handler:    _WisdomService_WisdomOne_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/wisdow.proto",
}
