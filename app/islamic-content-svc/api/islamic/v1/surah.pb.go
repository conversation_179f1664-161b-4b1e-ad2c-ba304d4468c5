// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: islamic/v1/surah.proto

package islamicv1

import (
	common "halalplus/api/common"
	pbentity "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 古兰经-章-列表
type SurahListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"章节id"`                                // 章节id
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"名称"`                               //名称
	IsPopular     uint32                 `protobuf:"varint,3,opt,name=is_popular,json=isPopular,proto3" json:"is_popular,omitempty" dc:"是否热门"` // 是否热门
	Page          *common.PageRequest    `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                             // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahListReq) Reset() {
	*x = SurahListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListReq) ProtoMessage() {}

func (x *SurahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListReq.ProtoReflect.Descriptor instead.
func (*SurahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{0}
}

func (x *SurahListReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SurahListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SurahListReq) GetIsPopular() uint32 {
	if x != nil {
		return x.IsPopular
	}
	return 0
}

func (x *SurahListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type SurahListResData struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	List          []*pbentity.SuratDaftar `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse    `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahListResData) Reset() {
	*x = SurahListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListResData) ProtoMessage() {}

func (x *SurahListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListResData.ProtoReflect.Descriptor instead.
func (*SurahListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{1}
}

func (x *SurahListResData) GetList() []*pbentity.SuratDaftar {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *SurahListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type SurahListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *SurahListResData      `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SurahListRes) Reset() {
	*x = SurahListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SurahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SurahListRes) ProtoMessage() {}

func (x *SurahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SurahListRes.ProtoReflect.Descriptor instead.
func (*SurahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{2}
}

func (x *SurahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SurahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SurahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SurahListRes) GetData() *SurahListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-节-列表
type JuzListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty" dc:"juz名称"` // juz名称
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JuzListReq) Reset() {
	*x = JuzListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JuzListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListReq) ProtoMessage() {}

func (x *JuzListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListReq.ProtoReflect.Descriptor instead.
func (*JuzListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{3}
}

func (x *JuzListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type JuzInfo struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	StartSurahId   uint32                 `protobuf:"varint,1,opt,name=start_surah_id,json=startSurahId,proto3" json:"start_surah_id,omitempty" dc:"开始章id"`        // 开始章id
	StartSurahName string                 `protobuf:"bytes,2,opt,name=start_surah_name,json=startSurahName,proto3" json:"start_surah_name,omitempty" dc:"开始章name"` // 开始章name
	EndSurahId     uint32                 `protobuf:"varint,3,opt,name=end_surah_id,json=endSurahId,proto3" json:"end_surah_id,omitempty" dc:"结束章id"`              // 结束章id
	EndSurahName   string                 `protobuf:"bytes,4,opt,name=end_surah_name,json=endSurahName,proto3" json:"end_surah_name,omitempty" dc:"结束章name"`       // 结束章name
	StartAyahId    uint32                 `protobuf:"varint,5,opt,name=start_ayah_id,json=startAyahId,proto3" json:"start_ayah_id,omitempty" dc:"开始节id"`           // 开始节id
	EndAyahId      uint32                 `protobuf:"varint,6,opt,name=end_ayah_id,json=endAyahId,proto3" json:"end_ayah_id,omitempty" dc:"结束节id"`                 // 结束节id
	Juz            string                 `protobuf:"bytes,7,opt,name=juz,proto3" json:"juz,omitempty" dc:"juz名称"`                                                 // juz名称
	FirstWord      string                 `protobuf:"bytes,8,opt,name=first_word,json=firstWord,proto3" json:"first_word,omitempty" dc:"对应经文的第一个单词"`               // 对应经文的第一个单词
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *JuzInfo) Reset() {
	*x = JuzInfo{}
	mi := &file_islamic_v1_surah_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JuzInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzInfo) ProtoMessage() {}

func (x *JuzInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzInfo.ProtoReflect.Descriptor instead.
func (*JuzInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{4}
}

func (x *JuzInfo) GetStartSurahId() uint32 {
	if x != nil {
		return x.StartSurahId
	}
	return 0
}

func (x *JuzInfo) GetStartSurahName() string {
	if x != nil {
		return x.StartSurahName
	}
	return ""
}

func (x *JuzInfo) GetEndSurahId() uint32 {
	if x != nil {
		return x.EndSurahId
	}
	return 0
}

func (x *JuzInfo) GetEndSurahName() string {
	if x != nil {
		return x.EndSurahName
	}
	return ""
}

func (x *JuzInfo) GetStartAyahId() uint32 {
	if x != nil {
		return x.StartAyahId
	}
	return 0
}

func (x *JuzInfo) GetEndAyahId() uint32 {
	if x != nil {
		return x.EndAyahId
	}
	return 0
}

func (x *JuzInfo) GetJuz() string {
	if x != nil {
		return x.Juz
	}
	return ""
}

func (x *JuzInfo) GetFirstWord() string {
	if x != nil {
		return x.FirstWord
	}
	return ""
}

type JuzListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*JuzInfo             `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JuzListResData) Reset() {
	*x = JuzListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JuzListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListResData) ProtoMessage() {}

func (x *JuzListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListResData.ProtoReflect.Descriptor instead.
func (*JuzListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{5}
}

func (x *JuzListResData) GetList() []*JuzInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type JuzListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *JuzListResData        `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *JuzListRes) Reset() {
	*x = JuzListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *JuzListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JuzListRes) ProtoMessage() {}

func (x *JuzListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JuzListRes.ProtoReflect.Descriptor instead.
func (*JuzListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{6}
}

func (x *JuzListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *JuzListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *JuzListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *JuzListRes) GetData() *JuzListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 古兰经-节-列表
type AyahListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"节id"`                                        // 节id
	SurahId       uint32                 `protobuf:"varint,2,opt,name=surah_id,json=surahId,proto3" json:"surah_id,omitempty" dc:"章节id"`              //章节id
	JuzId         uint32                 `protobuf:"varint,3,opt,name=juz_id,json=juzId,proto3" json:"juz_id,omitempty" dc:"juz_id"`                  //juz_id
	PageNumber    uint32                 `protobuf:"varint,4,opt,name=page_number,json=pageNumber,proto3" json:"page_number,omitempty" dc:"page 页数量"` //page 页数量
	Page          *common.PageRequest    `protobuf:"bytes,5,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                    // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahListReq) Reset() {
	*x = AyahListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListReq) ProtoMessage() {}

func (x *AyahListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListReq.ProtoReflect.Descriptor instead.
func (*AyahListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{7}
}

func (x *AyahListReq) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AyahListReq) GetSurahId() uint32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *AyahListReq) GetJuzId() uint32 {
	if x != nil {
		return x.JuzId
	}
	return 0
}

func (x *AyahListReq) GetPageNumber() uint32 {
	if x != nil {
		return x.PageNumber
	}
	return 0
}

func (x *AyahListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*pbentity.SuratAyat  `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahListResData) Reset() {
	*x = AyahListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListResData) ProtoMessage() {}

func (x *AyahListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListResData.ProtoReflect.Descriptor instead.
func (*AyahListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{8}
}

func (x *AyahListResData) GetList() []*pbentity.SuratAyat {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *AyahListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *AyahListResData       `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahListRes) Reset() {
	*x = AyahListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahListRes) ProtoMessage() {}

func (x *AyahListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahListRes.ProtoReflect.Descriptor instead.
func (*AyahListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{9}
}

func (x *AyahListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahListRes) GetData() *AyahListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AyahReadRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AyahId        uint32                 `protobuf:"varint,1,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"节id"`                    //节id
	IsUserOp      uint32                 `protobuf:"varint,2,opt,name=is_user_op,json=isUserOp,proto3" json:"is_user_op,omitempty" dc:"是否用户操作，1-是，0-否"` //是否用户操作，1-是，0-否
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadRecordReq) Reset() {
	*x = AyahReadRecordReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordReq) ProtoMessage() {}

func (x *AyahReadRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordReq.ProtoReflect.Descriptor instead.
func (*AyahReadRecordReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{10}
}

func (x *AyahReadRecordReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *AyahReadRecordReq) GetIsUserOp() uint32 {
	if x != nil {
		return x.IsUserOp
	}
	return 0
}

type AyahReadRecordRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadRecordRes) Reset() {
	*x = AyahReadRecordRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadRecordRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordRes) ProtoMessage() {}

func (x *AyahReadRecordRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordRes.ProtoReflect.Descriptor instead.
func (*AyahReadRecordRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{11}
}

func (x *AyahReadRecordRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadRecordRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadRecordRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type AyahReadCollectReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AyahId        uint32                 `protobuf:"varint,2,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"章节id"`            //章节id
	IsAdd         uint32                 `protobuf:"varint,3,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty" dc:"是否添加收藏，1-添加，0-取消收藏"` //是否添加收藏，1-添加，0-取消收藏
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadCollectReq) Reset() {
	*x = AyahReadCollectReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectReq) ProtoMessage() {}

func (x *AyahReadCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectReq.ProtoReflect.Descriptor instead.
func (*AyahReadCollectReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{12}
}

func (x *AyahReadCollectReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *AyahReadCollectReq) GetIsAdd() uint32 {
	if x != nil {
		return x.IsAdd
	}
	return 0
}

type AyahReadCollectRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadCollectRes) Reset() {
	*x = AyahReadCollectRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectRes) ProtoMessage() {}

func (x *AyahReadCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectRes.ProtoReflect.Descriptor instead.
func (*AyahReadCollectRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{13}
}

func (x *AyahReadCollectRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadCollectRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadCollectRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type CheckAyahReadCollectStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AyahId        uint32                 `protobuf:"varint,1,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"章节id"` //章节id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckAyahReadCollectStatusReq) Reset() {
	*x = CheckAyahReadCollectStatusReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckAyahReadCollectStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusReq) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusReq.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{14}
}

func (x *CheckAyahReadCollectStatusReq) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

type CheckAyahReadCollectStatusRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	IsCollect     int32                  `protobuf:"varint,4,opt,name=is_collect,json=isCollect,proto3" json:"is_collect,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckAyahReadCollectStatusRes) Reset() {
	*x = CheckAyahReadCollectStatusRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckAyahReadCollectStatusRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckAyahReadCollectStatusRes) ProtoMessage() {}

func (x *CheckAyahReadCollectStatusRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckAyahReadCollectStatusRes.ProtoReflect.Descriptor instead.
func (*CheckAyahReadCollectStatusRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{15}
}

func (x *CheckAyahReadCollectStatusRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckAyahReadCollectStatusRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckAyahReadCollectStatusRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CheckAyahReadCollectStatusRes) GetIsCollect() int32 {
	if x != nil {
		return x.IsCollect
	}
	return 0
}

type ReadInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SurahId       uint32                 `protobuf:"varint,1,opt,name=surah_id,json=surahId,proto3" json:"surah_id,omitempty" dc:"章id"`        // 章id
	SurahName     string                 `protobuf:"bytes,2,opt,name=surah_name,json=surahName,proto3" json:"surah_name,omitempty" dc:"章name"` // 章name
	AyahId        uint32                 `protobuf:"varint,3,opt,name=ayah_id,json=ayahId,proto3" json:"ayah_id,omitempty" dc:"节id"`           // 节id
	JuzId         uint32                 `protobuf:"varint,4,opt,name=juz_id,json=juzId,proto3" json:"juz_id,omitempty" dc:"juz-id"`           // juz-id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReadInfo) Reset() {
	*x = ReadInfo{}
	mi := &file_islamic_v1_surah_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReadInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReadInfo) ProtoMessage() {}

func (x *ReadInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReadInfo.ProtoReflect.Descriptor instead.
func (*ReadInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{16}
}

func (x *ReadInfo) GetSurahId() uint32 {
	if x != nil {
		return x.SurahId
	}
	return 0
}

func (x *ReadInfo) GetSurahName() string {
	if x != nil {
		return x.SurahName
	}
	return ""
}

func (x *ReadInfo) GetAyahId() uint32 {
	if x != nil {
		return x.AyahId
	}
	return 0
}

func (x *ReadInfo) GetJuzId() uint32 {
	if x != nil {
		return x.JuzId
	}
	return 0
}

type AyahReadRecordListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadRecordListReq) Reset() {
	*x = AyahReadRecordListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadRecordListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListReq) ProtoMessage() {}

func (x *AyahReadRecordListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListReq.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{17}
}

func (x *AyahReadRecordListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadRecordListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*ReadInfo            `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadRecordListResData) Reset() {
	*x = AyahReadRecordListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadRecordListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListResData) ProtoMessage() {}

func (x *AyahReadRecordListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListResData.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{18}
}

func (x *AyahReadRecordListResData) GetList() []*ReadInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *AyahReadRecordListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadRecordListRes struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	Code          int32                      `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                     `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error              `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *AyahReadRecordListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadRecordListRes) Reset() {
	*x = AyahReadRecordListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadRecordListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadRecordListRes) ProtoMessage() {}

func (x *AyahReadRecordListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadRecordListRes.ProtoReflect.Descriptor instead.
func (*AyahReadRecordListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{19}
}

func (x *AyahReadRecordListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadRecordListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadRecordListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahReadRecordListRes) GetData() *AyahReadRecordListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AyahReadCollectListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadCollectListReq) Reset() {
	*x = AyahReadCollectListReq{}
	mi := &file_islamic_v1_surah_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadCollectListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListReq) ProtoMessage() {}

func (x *AyahReadCollectListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListReq.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{20}
}

func (x *AyahReadCollectListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadCollectListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*ReadInfo            `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadCollectListResData) Reset() {
	*x = AyahReadCollectListResData{}
	mi := &file_islamic_v1_surah_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadCollectListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListResData) ProtoMessage() {}

func (x *AyahReadCollectListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListResData.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{21}
}

func (x *AyahReadCollectListResData) GetList() []*ReadInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *AyahReadCollectListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type AyahReadCollectListRes struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Code          int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error               `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *AyahReadCollectListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AyahReadCollectListRes) Reset() {
	*x = AyahReadCollectListRes{}
	mi := &file_islamic_v1_surah_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AyahReadCollectListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AyahReadCollectListRes) ProtoMessage() {}

func (x *AyahReadCollectListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_surah_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AyahReadCollectListRes.ProtoReflect.Descriptor instead.
func (*AyahReadCollectListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_surah_proto_rawDescGZIP(), []int{22}
}

func (x *AyahReadCollectListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AyahReadCollectListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AyahReadCollectListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AyahReadCollectListRes) GetData() *AyahReadCollectListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_surah_proto protoreflect.FileDescriptor

const file_islamic_v1_surah_proto_rawDesc = "" +
	"\n" +
	"\x16islamic/v1/surah.proto\x12\n" +
	"islamic.v1\x1a\x19pbentity/surat_ayat.proto\x1a\x1bpbentity/surat_daftar.proto\x1a\x1bpbentity/surat_tafsir.proto\x1a\x17common/front_info.proto\x1a\x11common/base.proto\x1a\x1egoogle/protobuf/wrappers.proto\"z\n" +
	"\fSurahListReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"is_popular\x18\x03 \x01(\rR\tisPopular\x12'\n" +
	"\x04page\x18\x04 \x01(\v2\x13.common.PageRequestR\x04page\"g\n" +
	"\x10SurahListResData\x12)\n" +
	"\x04list\x18\x01 \x03(\v2\x15.pbentity.SuratDaftarR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x8b\x01\n" +
	"\fSurahListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x120\n" +
	"\x04data\x18\x04 \x01(\v2\x1c.islamic.v1.SurahListResDataR\x04data\" \n" +
	"\n" +
	"JuzListReq\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\"\x96\x02\n" +
	"\aJuzInfo\x12$\n" +
	"\x0estart_surah_id\x18\x01 \x01(\rR\fstartSurahId\x12(\n" +
	"\x10start_surah_name\x18\x02 \x01(\tR\x0estartSurahName\x12 \n" +
	"\fend_surah_id\x18\x03 \x01(\rR\n" +
	"endSurahId\x12$\n" +
	"\x0eend_surah_name\x18\x04 \x01(\tR\fendSurahName\x12\"\n" +
	"\rstart_ayah_id\x18\x05 \x01(\rR\vstartAyahId\x12\x1e\n" +
	"\vend_ayah_id\x18\x06 \x01(\rR\tendAyahId\x12\x10\n" +
	"\x03juz\x18\a \x01(\tR\x03juz\x12\x1d\n" +
	"\n" +
	"first_word\x18\b \x01(\tR\tfirstWord\"9\n" +
	"\x0eJuzListResData\x12'\n" +
	"\x04list\x18\x01 \x03(\v2\x13.islamic.v1.JuzInfoR\x04list\"\x87\x01\n" +
	"\n" +
	"JuzListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12.\n" +
	"\x04data\x18\x04 \x01(\v2\x1a.islamic.v1.JuzListResDataR\x04data\"\x99\x01\n" +
	"\vAyahListReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x19\n" +
	"\bsurah_id\x18\x02 \x01(\rR\asurahId\x12\x15\n" +
	"\x06juz_id\x18\x03 \x01(\rR\x05juzId\x12\x1f\n" +
	"\vpage_number\x18\x04 \x01(\rR\n" +
	"pageNumber\x12'\n" +
	"\x04page\x18\x05 \x01(\v2\x13.common.PageRequestR\x04page\"d\n" +
	"\x0fAyahListResData\x12'\n" +
	"\x04list\x18\x01 \x03(\v2\x13.pbentity.SuratAyatR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x89\x01\n" +
	"\vAyahListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12/\n" +
	"\x04data\x18\x04 \x01(\v2\x1b.islamic.v1.AyahListResDataR\x04data\"J\n" +
	"\x11AyahReadRecordReq\x12\x17\n" +
	"\aayah_id\x18\x01 \x01(\rR\x06ayahId\x12\x1c\n" +
	"\n" +
	"is_user_op\x18\x02 \x01(\rR\bisUserOp\"^\n" +
	"\x11AyahReadRecordRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"D\n" +
	"\x12AyahReadCollectReq\x12\x17\n" +
	"\aayah_id\x18\x02 \x01(\rR\x06ayahId\x12\x15\n" +
	"\x06is_add\x18\x03 \x01(\rR\x05isAdd\"_\n" +
	"\x12AyahReadCollectRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"8\n" +
	"\x1dCheckAyahReadCollectStatusReq\x12\x17\n" +
	"\aayah_id\x18\x01 \x01(\rR\x06ayahId\"\x89\x01\n" +
	"\x1dCheckAyahReadCollectStatusRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12\x1d\n" +
	"\n" +
	"is_collect\x18\x04 \x01(\x05R\tisCollect\"t\n" +
	"\bReadInfo\x12\x19\n" +
	"\bsurah_id\x18\x01 \x01(\rR\asurahId\x12\x1d\n" +
	"\n" +
	"surah_name\x18\x02 \x01(\tR\tsurahName\x12\x17\n" +
	"\aayah_id\x18\x03 \x01(\rR\x06ayahId\x12\x15\n" +
	"\x06juz_id\x18\x04 \x01(\rR\x05juzId\"@\n" +
	"\x15AyahReadRecordListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\"o\n" +
	"\x19AyahReadRecordListResData\x12(\n" +
	"\x04list\x18\x01 \x03(\v2\x14.islamic.v1.ReadInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x9d\x01\n" +
	"\x15AyahReadRecordListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x129\n" +
	"\x04data\x18\x04 \x01(\v2%.islamic.v1.AyahReadRecordListResDataR\x04data\"A\n" +
	"\x16AyahReadCollectListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\"p\n" +
	"\x1aAyahReadCollectListResData\x12(\n" +
	"\x04list\x18\x01 \x03(\v2\x14.islamic.v1.ReadInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x9f\x01\n" +
	"\x16AyahReadCollectListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12:\n" +
	"\x04data\x18\x04 \x01(\v2&.islamic.v1.AyahReadCollectListResDataR\x04data2\x9a\x05\n" +
	"\fSurahService\x12?\n" +
	"\tSurahList\x12\x18.islamic.v1.SurahListReq\x1a\x18.islamic.v1.SurahListRes\x129\n" +
	"\aJuzList\x12\x16.islamic.v1.JuzListReq\x1a\x16.islamic.v1.JuzListRes\x12<\n" +
	"\bAyahList\x12\x17.islamic.v1.AyahListReq\x1a\x17.islamic.v1.AyahListRes\x12N\n" +
	"\x0eAyahReadRecord\x12\x1d.islamic.v1.AyahReadRecordReq\x1a\x1d.islamic.v1.AyahReadRecordRes\x12Z\n" +
	"\x12AyahReadRecordList\x12!.islamic.v1.AyahReadRecordListReq\x1a!.islamic.v1.AyahReadRecordListRes\x12r\n" +
	"\x1aCheckAyahReadCollectStatus\x12).islamic.v1.CheckAyahReadCollectStatusReq\x1a).islamic.v1.CheckAyahReadCollectStatusRes\x12Q\n" +
	"\x0fAyahReadCollect\x12\x1e.islamic.v1.AyahReadCollectReq\x1a\x1e.islamic.v1.AyahReadCollectRes\x12]\n" +
	"\x13AyahReadCollectList\x12\".islamic.v1.AyahReadCollectListReq\x1a\".islamic.v1.AyahReadCollectListResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_surah_proto_rawDescOnce sync.Once
	file_islamic_v1_surah_proto_rawDescData []byte
)

func file_islamic_v1_surah_proto_rawDescGZIP() []byte {
	file_islamic_v1_surah_proto_rawDescOnce.Do(func() {
		file_islamic_v1_surah_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_surah_proto_rawDesc), len(file_islamic_v1_surah_proto_rawDesc)))
	})
	return file_islamic_v1_surah_proto_rawDescData
}

var file_islamic_v1_surah_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_islamic_v1_surah_proto_goTypes = []any{
	(*SurahListReq)(nil),                  // 0: islamic.v1.SurahListReq
	(*SurahListResData)(nil),              // 1: islamic.v1.SurahListResData
	(*SurahListRes)(nil),                  // 2: islamic.v1.SurahListRes
	(*JuzListReq)(nil),                    // 3: islamic.v1.JuzListReq
	(*JuzInfo)(nil),                       // 4: islamic.v1.JuzInfo
	(*JuzListResData)(nil),                // 5: islamic.v1.JuzListResData
	(*JuzListRes)(nil),                    // 6: islamic.v1.JuzListRes
	(*AyahListReq)(nil),                   // 7: islamic.v1.AyahListReq
	(*AyahListResData)(nil),               // 8: islamic.v1.AyahListResData
	(*AyahListRes)(nil),                   // 9: islamic.v1.AyahListRes
	(*AyahReadRecordReq)(nil),             // 10: islamic.v1.AyahReadRecordReq
	(*AyahReadRecordRes)(nil),             // 11: islamic.v1.AyahReadRecordRes
	(*AyahReadCollectReq)(nil),            // 12: islamic.v1.AyahReadCollectReq
	(*AyahReadCollectRes)(nil),            // 13: islamic.v1.AyahReadCollectRes
	(*CheckAyahReadCollectStatusReq)(nil), // 14: islamic.v1.CheckAyahReadCollectStatusReq
	(*CheckAyahReadCollectStatusRes)(nil), // 15: islamic.v1.CheckAyahReadCollectStatusRes
	(*ReadInfo)(nil),                      // 16: islamic.v1.ReadInfo
	(*AyahReadRecordListReq)(nil),         // 17: islamic.v1.AyahReadRecordListReq
	(*AyahReadRecordListResData)(nil),     // 18: islamic.v1.AyahReadRecordListResData
	(*AyahReadRecordListRes)(nil),         // 19: islamic.v1.AyahReadRecordListRes
	(*AyahReadCollectListReq)(nil),        // 20: islamic.v1.AyahReadCollectListReq
	(*AyahReadCollectListResData)(nil),    // 21: islamic.v1.AyahReadCollectListResData
	(*AyahReadCollectListRes)(nil),        // 22: islamic.v1.AyahReadCollectListRes
	(*common.PageRequest)(nil),            // 23: common.PageRequest
	(*pbentity.SuratDaftar)(nil),          // 24: pbentity.SuratDaftar
	(*common.PageResponse)(nil),           // 25: common.PageResponse
	(*common.Error)(nil),                  // 26: common.Error
	(*pbentity.SuratAyat)(nil),            // 27: pbentity.SuratAyat
}
var file_islamic_v1_surah_proto_depIdxs = []int32{
	23, // 0: islamic.v1.SurahListReq.page:type_name -> common.PageRequest
	24, // 1: islamic.v1.SurahListResData.list:type_name -> pbentity.SuratDaftar
	25, // 2: islamic.v1.SurahListResData.page:type_name -> common.PageResponse
	26, // 3: islamic.v1.SurahListRes.error:type_name -> common.Error
	1,  // 4: islamic.v1.SurahListRes.data:type_name -> islamic.v1.SurahListResData
	4,  // 5: islamic.v1.JuzListResData.list:type_name -> islamic.v1.JuzInfo
	26, // 6: islamic.v1.JuzListRes.error:type_name -> common.Error
	5,  // 7: islamic.v1.JuzListRes.data:type_name -> islamic.v1.JuzListResData
	23, // 8: islamic.v1.AyahListReq.page:type_name -> common.PageRequest
	27, // 9: islamic.v1.AyahListResData.list:type_name -> pbentity.SuratAyat
	25, // 10: islamic.v1.AyahListResData.page:type_name -> common.PageResponse
	26, // 11: islamic.v1.AyahListRes.error:type_name -> common.Error
	8,  // 12: islamic.v1.AyahListRes.data:type_name -> islamic.v1.AyahListResData
	26, // 13: islamic.v1.AyahReadRecordRes.error:type_name -> common.Error
	26, // 14: islamic.v1.AyahReadCollectRes.error:type_name -> common.Error
	26, // 15: islamic.v1.CheckAyahReadCollectStatusRes.error:type_name -> common.Error
	23, // 16: islamic.v1.AyahReadRecordListReq.page:type_name -> common.PageRequest
	16, // 17: islamic.v1.AyahReadRecordListResData.list:type_name -> islamic.v1.ReadInfo
	25, // 18: islamic.v1.AyahReadRecordListResData.page:type_name -> common.PageResponse
	26, // 19: islamic.v1.AyahReadRecordListRes.error:type_name -> common.Error
	18, // 20: islamic.v1.AyahReadRecordListRes.data:type_name -> islamic.v1.AyahReadRecordListResData
	23, // 21: islamic.v1.AyahReadCollectListReq.page:type_name -> common.PageRequest
	16, // 22: islamic.v1.AyahReadCollectListResData.list:type_name -> islamic.v1.ReadInfo
	25, // 23: islamic.v1.AyahReadCollectListResData.page:type_name -> common.PageResponse
	26, // 24: islamic.v1.AyahReadCollectListRes.error:type_name -> common.Error
	21, // 25: islamic.v1.AyahReadCollectListRes.data:type_name -> islamic.v1.AyahReadCollectListResData
	0,  // 26: islamic.v1.SurahService.SurahList:input_type -> islamic.v1.SurahListReq
	3,  // 27: islamic.v1.SurahService.JuzList:input_type -> islamic.v1.JuzListReq
	7,  // 28: islamic.v1.SurahService.AyahList:input_type -> islamic.v1.AyahListReq
	10, // 29: islamic.v1.SurahService.AyahReadRecord:input_type -> islamic.v1.AyahReadRecordReq
	17, // 30: islamic.v1.SurahService.AyahReadRecordList:input_type -> islamic.v1.AyahReadRecordListReq
	14, // 31: islamic.v1.SurahService.CheckAyahReadCollectStatus:input_type -> islamic.v1.CheckAyahReadCollectStatusReq
	12, // 32: islamic.v1.SurahService.AyahReadCollect:input_type -> islamic.v1.AyahReadCollectReq
	20, // 33: islamic.v1.SurahService.AyahReadCollectList:input_type -> islamic.v1.AyahReadCollectListReq
	2,  // 34: islamic.v1.SurahService.SurahList:output_type -> islamic.v1.SurahListRes
	6,  // 35: islamic.v1.SurahService.JuzList:output_type -> islamic.v1.JuzListRes
	9,  // 36: islamic.v1.SurahService.AyahList:output_type -> islamic.v1.AyahListRes
	11, // 37: islamic.v1.SurahService.AyahReadRecord:output_type -> islamic.v1.AyahReadRecordRes
	19, // 38: islamic.v1.SurahService.AyahReadRecordList:output_type -> islamic.v1.AyahReadRecordListRes
	15, // 39: islamic.v1.SurahService.CheckAyahReadCollectStatus:output_type -> islamic.v1.CheckAyahReadCollectStatusRes
	13, // 40: islamic.v1.SurahService.AyahReadCollect:output_type -> islamic.v1.AyahReadCollectRes
	22, // 41: islamic.v1.SurahService.AyahReadCollectList:output_type -> islamic.v1.AyahReadCollectListRes
	34, // [34:42] is the sub-list for method output_type
	26, // [26:34] is the sub-list for method input_type
	26, // [26:26] is the sub-list for extension type_name
	26, // [26:26] is the sub-list for extension extendee
	0,  // [0:26] is the sub-list for field type_name
}

func init() { file_islamic_v1_surah_proto_init() }
func file_islamic_v1_surah_proto_init() {
	if File_islamic_v1_surah_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_surah_proto_rawDesc), len(file_islamic_v1_surah_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_surah_proto_goTypes,
		DependencyIndexes: file_islamic_v1_surah_proto_depIdxs,
		MessageInfos:      file_islamic_v1_surah_proto_msgTypes,
	}.Build()
	File_islamic_v1_surah_proto = out.File
	file_islamic_v1_surah_proto_goTypes = nil
	file_islamic_v1_surah_proto_depIdxs = nil
}
