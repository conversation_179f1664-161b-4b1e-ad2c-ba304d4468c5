// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.1
// source: islamic/v1/video.proto

package islamicv1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 视频播放列表信息
type VideoPlaylist struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"播放列表ID"`                                         // 播放列表ID
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"播放列表名称（当前语言）"`                                // 播放列表名称（当前语言）
	ShortTitle    string                 `protobuf:"bytes,3,opt,name=short_title,json=shortTitle,proto3" json:"short_title,omitempty" dc:"播放列表短标题（当前语言）"` // 播放列表短标题（当前语言）
	Description   string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" dc:"播放列表描述（当前语言）"`                  // 播放列表描述（当前语言）
	CoverUrl      string                 `protobuf:"bytes,5,opt,name=cover_url,json=coverUrl,proto3" json:"cover_url,omitempty" dc:"专题封面图片链接"`            // 专题封面图片链接
	VideoCount    uint32                 `protobuf:"varint,6,opt,name=video_count,json=videoCount,proto3" json:"video_count,omitempty" dc:"播放列表下视频数量"`    // 播放列表下视频数量
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlaylist) Reset() {
	*x = VideoPlaylist{}
	mi := &file_islamic_v1_video_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlaylist) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlaylist) ProtoMessage() {}

func (x *VideoPlaylist) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlaylist.ProtoReflect.Descriptor instead.
func (*VideoPlaylist) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{0}
}

func (x *VideoPlaylist) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VideoPlaylist) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VideoPlaylist) GetShortTitle() string {
	if x != nil {
		return x.ShortTitle
	}
	return ""
}

func (x *VideoPlaylist) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VideoPlaylist) GetCoverUrl() string {
	if x != nil {
		return x.CoverUrl
	}
	return ""
}

func (x *VideoPlaylist) GetVideoCount() uint32 {
	if x != nil {
		return x.VideoCount
	}
	return 0
}

// 视频播放列表列表请求
type VideoPlaylistListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlaylistListReq) Reset() {
	*x = VideoPlaylistListReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlaylistListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlaylistListReq) ProtoMessage() {}

func (x *VideoPlaylistListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlaylistListReq.ProtoReflect.Descriptor instead.
func (*VideoPlaylistListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{1}
}

func (x *VideoPlaylistListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 视频播放列表列表响应数据
type VideoPlaylistListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*VideoPlaylist       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"播放列表列表"` // 播放列表列表
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`   // 分页信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlaylistListResData) Reset() {
	*x = VideoPlaylistListResData{}
	mi := &file_islamic_v1_video_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlaylistListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlaylistListResData) ProtoMessage() {}

func (x *VideoPlaylistListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlaylistListResData.ProtoReflect.Descriptor instead.
func (*VideoPlaylistListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{2}
}

func (x *VideoPlaylistListResData) GetList() []*VideoPlaylist {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *VideoPlaylistListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 视频播放列表列表响应
type VideoPlaylistListRes struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Code          int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error             `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *VideoPlaylistListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlaylistListRes) Reset() {
	*x = VideoPlaylistListRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlaylistListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlaylistListRes) ProtoMessage() {}

func (x *VideoPlaylistListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlaylistListRes.ProtoReflect.Descriptor instead.
func (*VideoPlaylistListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{3}
}

func (x *VideoPlaylistListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoPlaylistListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoPlaylistListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoPlaylistListRes) GetData() *VideoPlaylistListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频列表项（用于列表展示的简化版本）
type VideoListItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VideoId       uint32                 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`                         // 视频ID
	CategoryId    uint32                 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类ID"`                // 分类ID
	Title         string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"视频标题（当前语言）"`                                       // 视频标题（当前语言）
	VideoCoverUrl string                 `protobuf:"bytes,4,opt,name=video_cover_url,json=videoCoverUrl,proto3" json:"video_cover_url,omitempty" dc:"视频封面图片URL"` // 视频封面图片URL
	VideoDuration uint32                 `protobuf:"varint,5,opt,name=video_duration,json=videoDuration,proto3" json:"video_duration,omitempty" dc:"视频时长(秒)"`    // 视频时长(秒)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoListItem) Reset() {
	*x = VideoListItem{}
	mi := &file_islamic_v1_video_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoListItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoListItem) ProtoMessage() {}

func (x *VideoListItem) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoListItem.ProtoReflect.Descriptor instead.
func (*VideoListItem) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{4}
}

func (x *VideoListItem) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *VideoListItem) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *VideoListItem) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *VideoListItem) GetVideoCoverUrl() string {
	if x != nil {
		return x.VideoCoverUrl
	}
	return ""
}

func (x *VideoListItem) GetVideoDuration() uint32 {
	if x != nil {
		return x.VideoDuration
	}
	return 0
}

// 视频详情信息（完整版本）
type Video struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	VideoId          uint32                 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`                                            // 视频ID
	CategoryId       uint32                 `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类ID"`                                   // 分类ID
	Title            string                 `protobuf:"bytes,3,opt,name=title,proto3" json:"title,omitempty" dc:"视频标题（当前语言）"`                                                          // 视频标题（当前语言）
	Description      string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty" dc:"视频描述（当前语言）"`                                              // 视频描述（当前语言）
	VideoCoverUrl    string                 `protobuf:"bytes,5,opt,name=video_cover_url,json=videoCoverUrl,proto3" json:"video_cover_url,omitempty" dc:"视频封面图片URL"`                    // 视频封面图片URL
	VideoUrl         string                 `protobuf:"bytes,6,opt,name=video_url,json=videoUrl,proto3" json:"video_url,omitempty" dc:"视频文件URL"`                                       // 视频文件URL
	Author           string                 `protobuf:"bytes,7,opt,name=author,proto3" json:"author,omitempty" dc:"视频作者"`                                                              // 视频作者
	AuthorLogo       string                 `protobuf:"bytes,8,opt,name=author_logo,json=authorLogo,proto3" json:"author_logo,omitempty" dc:"作者头像URL"`                                 // 作者头像URL
	AuthorAuthStatus uint32                 `protobuf:"varint,9,opt,name=author_auth_status,json=authorAuthStatus,proto3" json:"author_auth_status,omitempty" dc:"作者认证状态：0-未认证，1-已认证"` // 作者认证状态：0-未认证，1-已认证
	PublishState     uint32                 `protobuf:"varint,10,opt,name=publish_state,json=publishState,proto3" json:"publish_state,omitempty" dc:"发布状态：0-待发布，1-已发布，2-已下线"`          // 发布状态：0-待发布，1-已发布，2-已下线
	IsCollected      bool                   `protobuf:"varint,11,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty" dc:"当前用户是否已收藏（需要登录）"`                    // 当前用户是否已收藏（需要登录）
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Video) Reset() {
	*x = Video{}
	mi := &file_islamic_v1_video_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Video) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Video) ProtoMessage() {}

func (x *Video) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Video.ProtoReflect.Descriptor instead.
func (*Video) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{5}
}

func (x *Video) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *Video) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *Video) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *Video) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Video) GetVideoCoverUrl() string {
	if x != nil {
		return x.VideoCoverUrl
	}
	return ""
}

func (x *Video) GetVideoUrl() string {
	if x != nil {
		return x.VideoUrl
	}
	return ""
}

func (x *Video) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *Video) GetAuthorLogo() string {
	if x != nil {
		return x.AuthorLogo
	}
	return ""
}

func (x *Video) GetAuthorAuthStatus() uint32 {
	if x != nil {
		return x.AuthorAuthStatus
	}
	return 0
}

func (x *Video) GetPublishState() uint32 {
	if x != nil {
		return x.PublishState
	}
	return 0
}

func (x *Video) GetIsCollected() bool {
	if x != nil {
		return x.IsCollected
	}
	return false
}

// 分类视频列表请求
type CategoryVideoListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CategoryId    uint32                 `protobuf:"varint,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类ID（必填）"`                     // 分类ID（必填）
	SortBy        string                 `protobuf:"bytes,2,opt,name=sort_by,json=sortBy,proto3" json:"sort_by,omitempty" dc:"排序方式：view_count, created_at, published_at"` // 排序方式：view_count, created_at, published_at
	SortOrder     string                 `protobuf:"bytes,3,opt,name=sort_order,json=sortOrder,proto3" json:"sort_order,omitempty" dc:"排序顺序：asc, desc"`                   // 排序顺序：asc, desc
	Page          *common.PageRequest    `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                                        // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CategoryVideoListReq) Reset() {
	*x = CategoryVideoListReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CategoryVideoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryVideoListReq) ProtoMessage() {}

func (x *CategoryVideoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryVideoListReq.ProtoReflect.Descriptor instead.
func (*CategoryVideoListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{6}
}

func (x *CategoryVideoListReq) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CategoryVideoListReq) GetSortBy() string {
	if x != nil {
		return x.SortBy
	}
	return ""
}

func (x *CategoryVideoListReq) GetSortOrder() string {
	if x != nil {
		return x.SortOrder
	}
	return ""
}

func (x *CategoryVideoListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 分类视频列表响应数据
type CategoryVideoListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*VideoListItem       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"视频列表"` // 视频列表
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"` // 分页信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CategoryVideoListResData) Reset() {
	*x = CategoryVideoListResData{}
	mi := &file_islamic_v1_video_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CategoryVideoListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryVideoListResData) ProtoMessage() {}

func (x *CategoryVideoListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryVideoListResData.ProtoReflect.Descriptor instead.
func (*CategoryVideoListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{7}
}

func (x *CategoryVideoListResData) GetList() []*VideoListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *CategoryVideoListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 分类视频列表响应
type CategoryVideoListRes struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Code          int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error             `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *CategoryVideoListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CategoryVideoListRes) Reset() {
	*x = CategoryVideoListRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CategoryVideoListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryVideoListRes) ProtoMessage() {}

func (x *CategoryVideoListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryVideoListRes.ProtoReflect.Descriptor instead.
func (*CategoryVideoListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{8}
}

func (x *CategoryVideoListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CategoryVideoListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CategoryVideoListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CategoryVideoListRes) GetData() *CategoryVideoListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频详情请求
type VideoDetailReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VideoId       uint32                 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"` // 视频ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoDetailReq) Reset() {
	*x = VideoDetailReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoDetailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoDetailReq) ProtoMessage() {}

func (x *VideoDetailReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoDetailReq.ProtoReflect.Descriptor instead.
func (*VideoDetailReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{9}
}

func (x *VideoDetailReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

// 视频详情响应
type VideoDetailRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *Video                 `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoDetailRes) Reset() {
	*x = VideoDetailRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoDetailRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoDetailRes) ProtoMessage() {}

func (x *VideoDetailRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoDetailRes.ProtoReflect.Descriptor instead.
func (*VideoDetailRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{10}
}

func (x *VideoDetailRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoDetailRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoDetailRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoDetailRes) GetData() *Video {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频收藏请求
type VideoCollectReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VideoId       uint32                 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`         // 视频ID
	IsAdd         uint32                 `protobuf:"varint,2,opt,name=is_add,json=isAdd,proto3" json:"is_add,omitempty" dc:"是否添加收藏，1-添加，0-取消收藏"` // 是否添加收藏，1-添加，0-取消收藏
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectReq) Reset() {
	*x = VideoCollectReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectReq) ProtoMessage() {}

func (x *VideoCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectReq.ProtoReflect.Descriptor instead.
func (*VideoCollectReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{11}
}

func (x *VideoCollectReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *VideoCollectReq) GetIsAdd() uint32 {
	if x != nil {
		return x.IsAdd
	}
	return 0
}

// 视频收藏响应
type VideoCollectRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectRes) Reset() {
	*x = VideoCollectRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectRes) ProtoMessage() {}

func (x *VideoCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectRes.ProtoReflect.Descriptor instead.
func (*VideoCollectRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{12}
}

func (x *VideoCollectRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoCollectRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoCollectRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 检查视频收藏状态请求
type CheckVideoCollectStatusReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VideoId       uint32                 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"` // 视频ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckVideoCollectStatusReq) Reset() {
	*x = CheckVideoCollectStatusReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckVideoCollectStatusReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVideoCollectStatusReq) ProtoMessage() {}

func (x *CheckVideoCollectStatusReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVideoCollectStatusReq.ProtoReflect.Descriptor instead.
func (*CheckVideoCollectStatusReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{13}
}

func (x *CheckVideoCollectStatusReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

// 检查视频收藏状态响应
type CheckVideoCollectStatusRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	IsCollected   bool                   `protobuf:"varint,4,opt,name=is_collected,json=isCollected,proto3" json:"is_collected,omitempty" dc:"是否已收藏"` // 是否已收藏
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckVideoCollectStatusRes) Reset() {
	*x = CheckVideoCollectStatusRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckVideoCollectStatusRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVideoCollectStatusRes) ProtoMessage() {}

func (x *CheckVideoCollectStatusRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVideoCollectStatusRes.ProtoReflect.Descriptor instead.
func (*CheckVideoCollectStatusRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{14}
}

func (x *CheckVideoCollectStatusRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CheckVideoCollectStatusRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *CheckVideoCollectStatusRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *CheckVideoCollectStatusRes) GetIsCollected() bool {
	if x != nil {
		return x.IsCollected
	}
	return false
}

// 视频收藏列表请求
type VideoCollectListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectListReq) Reset() {
	*x = VideoCollectListReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectListReq) ProtoMessage() {}

func (x *VideoCollectListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectListReq.ProtoReflect.Descriptor instead.
func (*VideoCollectListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{15}
}

func (x *VideoCollectListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 视频收藏列表响应数据
type VideoCollectListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*VideoListItem       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"收藏的视频列表"` // 收藏的视频列表
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`    // 分页信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectListResData) Reset() {
	*x = VideoCollectListResData{}
	mi := &file_islamic_v1_video_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectListResData) ProtoMessage() {}

func (x *VideoCollectListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectListResData.ProtoReflect.Descriptor instead.
func (*VideoCollectListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{16}
}

func (x *VideoCollectListResData) GetList() []*VideoListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *VideoCollectListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 视频收藏列表响应
type VideoCollectListRes struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Code          int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error            `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *VideoCollectListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoCollectListRes) Reset() {
	*x = VideoCollectListRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoCollectListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoCollectListRes) ProtoMessage() {}

func (x *VideoCollectListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoCollectListRes.ProtoReflect.Descriptor instead.
func (*VideoCollectListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{17}
}

func (x *VideoCollectListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoCollectListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoCollectListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoCollectListRes) GetData() *VideoCollectListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 视频分享请求
type VideoShareReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VideoId       uint32                 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`                                                       // 视频ID
	SharePlatform string                 `protobuf:"bytes,2,opt,name=share_platform,json=sharePlatform,proto3" json:"share_platform,omitempty" dc:"分享平台：wechat, facebook, twitter, whatsapp等"` // 分享平台：wechat, facebook, twitter, whatsapp等
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoShareReq) Reset() {
	*x = VideoShareReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoShareReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoShareReq) ProtoMessage() {}

func (x *VideoShareReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoShareReq.ProtoReflect.Descriptor instead.
func (*VideoShareReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{18}
}

func (x *VideoShareReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *VideoShareReq) GetSharePlatform() string {
	if x != nil {
		return x.SharePlatform
	}
	return ""
}

// 视频分享响应
type VideoShareRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoShareRes) Reset() {
	*x = VideoShareRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoShareRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoShareRes) ProtoMessage() {}

func (x *VideoShareRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoShareRes.ProtoReflect.Descriptor instead.
func (*VideoShareRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{19}
}

func (x *VideoShareRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoShareRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoShareRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 视频播放历史信息
type VideoPlayHistory struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"历史记录ID"`                                              // 历史记录ID
	VideoId       uint32                 `protobuf:"varint,2,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`                       // 视频ID
	Video         *VideoListItem         `protobuf:"bytes,3,opt,name=video,proto3" json:"video,omitempty" dc:"视频信息（简化版）"`                                      // 视频信息（简化版）
	PlayPosition  uint32                 `protobuf:"varint,4,opt,name=play_position,json=playPosition,proto3" json:"play_position,omitempty" dc:"播放位置(秒)"`     // 播放位置(秒)
	PlayDuration  uint32                 `protobuf:"varint,5,opt,name=play_duration,json=playDuration,proto3" json:"play_duration,omitempty" dc:"本次播放时长(秒)"`   // 本次播放时长(秒)
	IsCompleted   uint32                 `protobuf:"varint,6,opt,name=is_completed,json=isCompleted,proto3" json:"is_completed,omitempty" dc:"是否播放完成，0-否，1-是"` // 是否播放完成，0-否，1-是
	CreatedAt     int64                  `protobuf:"varint,7,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty" dc:"播放时间戳"`                // 播放时间戳
	UpdatedAt     int64                  `protobuf:"varint,8,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty" dc:"更新时间戳"`                // 更新时间戳
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlayHistory) Reset() {
	*x = VideoPlayHistory{}
	mi := &file_islamic_v1_video_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlayHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayHistory) ProtoMessage() {}

func (x *VideoPlayHistory) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayHistory.ProtoReflect.Descriptor instead.
func (*VideoPlayHistory) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{20}
}

func (x *VideoPlayHistory) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *VideoPlayHistory) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *VideoPlayHistory) GetVideo() *VideoListItem {
	if x != nil {
		return x.Video
	}
	return nil
}

func (x *VideoPlayHistory) GetPlayPosition() uint32 {
	if x != nil {
		return x.PlayPosition
	}
	return 0
}

func (x *VideoPlayHistory) GetPlayDuration() uint32 {
	if x != nil {
		return x.PlayDuration
	}
	return 0
}

func (x *VideoPlayHistory) GetIsCompleted() uint32 {
	if x != nil {
		return x.IsCompleted
	}
	return 0
}

func (x *VideoPlayHistory) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *VideoPlayHistory) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// 记录视频播放历史请求
type VideoPlayHistoryRecordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VideoId       uint32                 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"`                       // 视频ID
	PlayPosition  uint32                 `protobuf:"varint,2,opt,name=play_position,json=playPosition,proto3" json:"play_position,omitempty" dc:"播放位置(秒)"`     // 播放位置(秒)
	PlayDuration  uint32                 `protobuf:"varint,3,opt,name=play_duration,json=playDuration,proto3" json:"play_duration,omitempty" dc:"本次播放时长(秒)"`   // 本次播放时长(秒)
	IsCompleted   uint32                 `protobuf:"varint,4,opt,name=is_completed,json=isCompleted,proto3" json:"is_completed,omitempty" dc:"是否播放完成，0-否，1-是"` // 是否播放完成，0-否，1-是
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlayHistoryRecordReq) Reset() {
	*x = VideoPlayHistoryRecordReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlayHistoryRecordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayHistoryRecordReq) ProtoMessage() {}

func (x *VideoPlayHistoryRecordReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayHistoryRecordReq.ProtoReflect.Descriptor instead.
func (*VideoPlayHistoryRecordReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{21}
}

func (x *VideoPlayHistoryRecordReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

func (x *VideoPlayHistoryRecordReq) GetPlayPosition() uint32 {
	if x != nil {
		return x.PlayPosition
	}
	return 0
}

func (x *VideoPlayHistoryRecordReq) GetPlayDuration() uint32 {
	if x != nil {
		return x.PlayDuration
	}
	return 0
}

func (x *VideoPlayHistoryRecordReq) GetIsCompleted() uint32 {
	if x != nil {
		return x.IsCompleted
	}
	return 0
}

// 记录视频播放历史响应
type VideoPlayHistoryRecordRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlayHistoryRecordRes) Reset() {
	*x = VideoPlayHistoryRecordRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlayHistoryRecordRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayHistoryRecordRes) ProtoMessage() {}

func (x *VideoPlayHistoryRecordRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayHistoryRecordRes.ProtoReflect.Descriptor instead.
func (*VideoPlayHistoryRecordRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{22}
}

func (x *VideoPlayHistoryRecordRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoPlayHistoryRecordRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoPlayHistoryRecordRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 视频播放历史列表请求
type VideoPlayHistoryListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlayHistoryListReq) Reset() {
	*x = VideoPlayHistoryListReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlayHistoryListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayHistoryListReq) ProtoMessage() {}

func (x *VideoPlayHistoryListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayHistoryListReq.ProtoReflect.Descriptor instead.
func (*VideoPlayHistoryListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{23}
}

func (x *VideoPlayHistoryListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 视频播放历史列表响应数据
type VideoPlayHistoryListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*VideoPlayHistory    `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"播放历史列表"` // 播放历史列表
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`   // 分页信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlayHistoryListResData) Reset() {
	*x = VideoPlayHistoryListResData{}
	mi := &file_islamic_v1_video_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlayHistoryListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayHistoryListResData) ProtoMessage() {}

func (x *VideoPlayHistoryListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayHistoryListResData.ProtoReflect.Descriptor instead.
func (*VideoPlayHistoryListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{24}
}

func (x *VideoPlayHistoryListResData) GetList() []*VideoPlayHistory {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *VideoPlayHistoryListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 视频播放历史列表响应
type VideoPlayHistoryListRes struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Code          int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error                `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *VideoPlayHistoryListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlayHistoryListRes) Reset() {
	*x = VideoPlayHistoryListRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlayHistoryListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayHistoryListRes) ProtoMessage() {}

func (x *VideoPlayHistoryListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayHistoryListRes.ProtoReflect.Descriptor instead.
func (*VideoPlayHistoryListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{25}
}

func (x *VideoPlayHistoryListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoPlayHistoryListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoPlayHistoryListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoPlayHistoryListRes) GetData() *VideoPlayHistoryListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 获取视频播放进度请求
type VideoPlayProgressReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	VideoId       uint32                 `protobuf:"varint,1,opt,name=video_id,json=videoId,proto3" json:"video_id,omitempty" dc:"视频ID"` // 视频ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlayProgressReq) Reset() {
	*x = VideoPlayProgressReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlayProgressReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayProgressReq) ProtoMessage() {}

func (x *VideoPlayProgressReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayProgressReq.ProtoReflect.Descriptor instead.
func (*VideoPlayProgressReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{26}
}

func (x *VideoPlayProgressReq) GetVideoId() uint32 {
	if x != nil {
		return x.VideoId
	}
	return 0
}

// 获取视频播放进度响应
type VideoPlayProgressRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	PlayPosition  uint32                 `protobuf:"varint,4,opt,name=play_position,json=playPosition,proto3" json:"play_position,omitempty" dc:"播放位置(秒)"`     // 播放位置(秒)
	IsCompleted   uint32                 `protobuf:"varint,5,opt,name=is_completed,json=isCompleted,proto3" json:"is_completed,omitempty" dc:"是否播放完成，0-否，1-是"` // 是否播放完成，0-否，1-是
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VideoPlayProgressRes) Reset() {
	*x = VideoPlayProgressRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VideoPlayProgressRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VideoPlayProgressRes) ProtoMessage() {}

func (x *VideoPlayProgressRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VideoPlayProgressRes.ProtoReflect.Descriptor instead.
func (*VideoPlayProgressRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{27}
}

func (x *VideoPlayProgressRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VideoPlayProgressRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VideoPlayProgressRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VideoPlayProgressRes) GetPlayPosition() uint32 {
	if x != nil {
		return x.PlayPosition
	}
	return 0
}

func (x *VideoPlayProgressRes) GetIsCompleted() uint32 {
	if x != nil {
		return x.IsCompleted
	}
	return 0
}

// 播放列表中的视频列表请求
type PlaylistVideoListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlaylistId    uint32                 `protobuf:"varint,1,opt,name=playlist_id,json=playlistId,proto3" json:"playlist_id,omitempty" dc:"播放列表ID"` // 播放列表ID
	Page          *common.PageRequest    `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                  // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaylistVideoListReq) Reset() {
	*x = PlaylistVideoListReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaylistVideoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistVideoListReq) ProtoMessage() {}

func (x *PlaylistVideoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistVideoListReq.ProtoReflect.Descriptor instead.
func (*PlaylistVideoListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{28}
}

func (x *PlaylistVideoListReq) GetPlaylistId() uint32 {
	if x != nil {
		return x.PlaylistId
	}
	return 0
}

func (x *PlaylistVideoListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 播放列表基本信息（用于PlaylistVideoList返回）
type PlaylistBasicInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlaylistId    uint32                 `protobuf:"varint,1,opt,name=playlist_id,json=playlistId,proto3" json:"playlist_id,omitempty" dc:"播放列表ID"` // 播放列表ID
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"播放列表名称（当前语言）"`                          // 播放列表名称（当前语言）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaylistBasicInfo) Reset() {
	*x = PlaylistBasicInfo{}
	mi := &file_islamic_v1_video_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaylistBasicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistBasicInfo) ProtoMessage() {}

func (x *PlaylistBasicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistBasicInfo.ProtoReflect.Descriptor instead.
func (*PlaylistBasicInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{29}
}

func (x *PlaylistBasicInfo) GetPlaylistId() uint32 {
	if x != nil {
		return x.PlaylistId
	}
	return 0
}

func (x *PlaylistBasicInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 播放列表中的视频列表响应数据
type PlaylistVideoListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Playlist      *PlaylistBasicInfo     `protobuf:"bytes,1,opt,name=playlist,proto3" json:"playlist,omitempty" dc:"播放列表基本信息"` // 播放列表基本信息
	List          []*VideoListItem       `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty" dc:"视频列表"`             // 视频列表
	Page          *common.PageResponse   `protobuf:"bytes,3,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`             // 分页信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaylistVideoListResData) Reset() {
	*x = PlaylistVideoListResData{}
	mi := &file_islamic_v1_video_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaylistVideoListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistVideoListResData) ProtoMessage() {}

func (x *PlaylistVideoListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistVideoListResData.ProtoReflect.Descriptor instead.
func (*PlaylistVideoListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{30}
}

func (x *PlaylistVideoListResData) GetPlaylist() *PlaylistBasicInfo {
	if x != nil {
		return x.Playlist
	}
	return nil
}

func (x *PlaylistVideoListResData) GetList() []*VideoListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *PlaylistVideoListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 播放列表中的视频列表响应
type PlaylistVideoListRes struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Code          int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error             `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *PlaylistVideoListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaylistVideoListRes) Reset() {
	*x = PlaylistVideoListRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaylistVideoListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaylistVideoListRes) ProtoMessage() {}

func (x *PlaylistVideoListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaylistVideoListRes.ProtoReflect.Descriptor instead.
func (*PlaylistVideoListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{31}
}

func (x *PlaylistVideoListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PlaylistVideoListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *PlaylistVideoListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *PlaylistVideoListRes) GetData() *PlaylistVideoListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 推荐视频列表请求
type RecommendedVideoListReq struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	CategoryId    *wrapperspb.UInt32Value `protobuf:"bytes,1,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类ID（可选）"` // 分类ID（可选）
	Page          *common.PageRequest     `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                   // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecommendedVideoListReq) Reset() {
	*x = RecommendedVideoListReq{}
	mi := &file_islamic_v1_video_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendedVideoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendedVideoListReq) ProtoMessage() {}

func (x *RecommendedVideoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendedVideoListReq.ProtoReflect.Descriptor instead.
func (*RecommendedVideoListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{32}
}

func (x *RecommendedVideoListReq) GetCategoryId() *wrapperspb.UInt32Value {
	if x != nil {
		return x.CategoryId
	}
	return nil
}

func (x *RecommendedVideoListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

// 推荐视频列表响应数据
type RecommendedVideoListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*VideoListItem       `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty" dc:"推荐视频列表"` // 推荐视频列表
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页信息"`   // 分页信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecommendedVideoListResData) Reset() {
	*x = RecommendedVideoListResData{}
	mi := &file_islamic_v1_video_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendedVideoListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendedVideoListResData) ProtoMessage() {}

func (x *RecommendedVideoListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendedVideoListResData.ProtoReflect.Descriptor instead.
func (*RecommendedVideoListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{33}
}

func (x *RecommendedVideoListResData) GetList() []*VideoListItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *RecommendedVideoListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

// 推荐视频列表响应
type RecommendedVideoListRes struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Code          int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error                `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *RecommendedVideoListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecommendedVideoListRes) Reset() {
	*x = RecommendedVideoListRes{}
	mi := &file_islamic_v1_video_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecommendedVideoListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecommendedVideoListRes) ProtoMessage() {}

func (x *RecommendedVideoListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_video_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecommendedVideoListRes.ProtoReflect.Descriptor instead.
func (*RecommendedVideoListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_video_proto_rawDescGZIP(), []int{34}
}

func (x *RecommendedVideoListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RecommendedVideoListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *RecommendedVideoListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *RecommendedVideoListRes) GetData() *RecommendedVideoListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_video_proto protoreflect.FileDescriptor

const file_islamic_v1_video_proto_rawDesc = "" +
	"\n" +
	"\x16islamic/v1/video.proto\x12\n" +
	"islamic.v1\x1a\x11common/base.proto\x1a\x1egoogle/protobuf/wrappers.proto\"\xb4\x01\n" +
	"\rVideoPlaylist\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1f\n" +
	"\vshort_title\x18\x03 \x01(\tR\n" +
	"shortTitle\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12\x1b\n" +
	"\tcover_url\x18\x05 \x01(\tR\bcoverUrl\x12\x1f\n" +
	"\vvideo_count\x18\x06 \x01(\rR\n" +
	"videoCount\"?\n" +
	"\x14VideoPlaylistListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\"s\n" +
	"\x18VideoPlaylistListResData\x12-\n" +
	"\x04list\x18\x01 \x03(\v2\x19.islamic.v1.VideoPlaylistR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x9b\x01\n" +
	"\x14VideoPlaylistListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x128\n" +
	"\x04data\x18\x04 \x01(\v2$.islamic.v1.VideoPlaylistListResDataR\x04data\"\xb0\x01\n" +
	"\rVideoListItem\x12\x19\n" +
	"\bvideo_id\x18\x01 \x01(\rR\avideoId\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\rR\n" +
	"categoryId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12&\n" +
	"\x0fvideo_cover_url\x18\x04 \x01(\tR\rvideoCoverUrl\x12%\n" +
	"\x0evideo_duration\x18\x05 \x01(\rR\rvideoDuration\"\xef\x02\n" +
	"\x05Video\x12\x19\n" +
	"\bvideo_id\x18\x01 \x01(\rR\avideoId\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\rR\n" +
	"categoryId\x12\x14\n" +
	"\x05title\x18\x03 \x01(\tR\x05title\x12 \n" +
	"\vdescription\x18\x04 \x01(\tR\vdescription\x12&\n" +
	"\x0fvideo_cover_url\x18\x05 \x01(\tR\rvideoCoverUrl\x12\x1b\n" +
	"\tvideo_url\x18\x06 \x01(\tR\bvideoUrl\x12\x16\n" +
	"\x06author\x18\a \x01(\tR\x06author\x12\x1f\n" +
	"\vauthor_logo\x18\b \x01(\tR\n" +
	"authorLogo\x12,\n" +
	"\x12author_auth_status\x18\t \x01(\rR\x10authorAuthStatus\x12#\n" +
	"\rpublish_state\x18\n" +
	" \x01(\rR\fpublishState\x12!\n" +
	"\fis_collected\x18\v \x01(\bR\visCollected\"\x98\x01\n" +
	"\x14CategoryVideoListReq\x12\x1f\n" +
	"\vcategory_id\x18\x01 \x01(\rR\n" +
	"categoryId\x12\x17\n" +
	"\asort_by\x18\x02 \x01(\tR\x06sortBy\x12\x1d\n" +
	"\n" +
	"sort_order\x18\x03 \x01(\tR\tsortOrder\x12'\n" +
	"\x04page\x18\x04 \x01(\v2\x13.common.PageRequestR\x04page\"s\n" +
	"\x18CategoryVideoListResData\x12-\n" +
	"\x04list\x18\x01 \x03(\v2\x19.islamic.v1.VideoListItemR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x9b\x01\n" +
	"\x14CategoryVideoListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x128\n" +
	"\x04data\x18\x04 \x01(\v2$.islamic.v1.CategoryVideoListResDataR\x04data\"+\n" +
	"\x0eVideoDetailReq\x12\x19\n" +
	"\bvideo_id\x18\x01 \x01(\rR\avideoId\"\x82\x01\n" +
	"\x0eVideoDetailRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12%\n" +
	"\x04data\x18\x04 \x01(\v2\x11.islamic.v1.VideoR\x04data\"C\n" +
	"\x0fVideoCollectReq\x12\x19\n" +
	"\bvideo_id\x18\x01 \x01(\rR\avideoId\x12\x15\n" +
	"\x06is_add\x18\x02 \x01(\rR\x05isAdd\"\\\n" +
	"\x0fVideoCollectRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"7\n" +
	"\x1aCheckVideoCollectStatusReq\x12\x19\n" +
	"\bvideo_id\x18\x01 \x01(\rR\avideoId\"\x8a\x01\n" +
	"\x1aCheckVideoCollectStatusRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12!\n" +
	"\fis_collected\x18\x04 \x01(\bR\visCollected\">\n" +
	"\x13VideoCollectListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\"r\n" +
	"\x17VideoCollectListResData\x12-\n" +
	"\x04list\x18\x01 \x03(\v2\x19.islamic.v1.VideoListItemR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x99\x01\n" +
	"\x13VideoCollectListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x127\n" +
	"\x04data\x18\x04 \x01(\v2#.islamic.v1.VideoCollectListResDataR\x04data\"Q\n" +
	"\rVideoShareReq\x12\x19\n" +
	"\bvideo_id\x18\x01 \x01(\rR\avideoId\x12%\n" +
	"\x0eshare_platform\x18\x02 \x01(\tR\rsharePlatform\"Z\n" +
	"\rVideoShareRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"\x99\x02\n" +
	"\x10VideoPlayHistory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x19\n" +
	"\bvideo_id\x18\x02 \x01(\rR\avideoId\x12/\n" +
	"\x05video\x18\x03 \x01(\v2\x19.islamic.v1.VideoListItemR\x05video\x12#\n" +
	"\rplay_position\x18\x04 \x01(\rR\fplayPosition\x12#\n" +
	"\rplay_duration\x18\x05 \x01(\rR\fplayDuration\x12!\n" +
	"\fis_completed\x18\x06 \x01(\rR\visCompleted\x12\x1d\n" +
	"\n" +
	"created_at\x18\a \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\b \x01(\x03R\tupdatedAt\"\xa3\x01\n" +
	"\x19VideoPlayHistoryRecordReq\x12\x19\n" +
	"\bvideo_id\x18\x01 \x01(\rR\avideoId\x12#\n" +
	"\rplay_position\x18\x02 \x01(\rR\fplayPosition\x12#\n" +
	"\rplay_duration\x18\x03 \x01(\rR\fplayDuration\x12!\n" +
	"\fis_completed\x18\x04 \x01(\rR\visCompleted\"f\n" +
	"\x19VideoPlayHistoryRecordRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"B\n" +
	"\x17VideoPlayHistoryListReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\"y\n" +
	"\x1bVideoPlayHistoryListResData\x120\n" +
	"\x04list\x18\x01 \x03(\v2\x1c.islamic.v1.VideoPlayHistoryR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\xa1\x01\n" +
	"\x17VideoPlayHistoryListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12;\n" +
	"\x04data\x18\x04 \x01(\v2'.islamic.v1.VideoPlayHistoryListResDataR\x04data\"1\n" +
	"\x14VideoPlayProgressReq\x12\x19\n" +
	"\bvideo_id\x18\x01 \x01(\rR\avideoId\"\xa9\x01\n" +
	"\x14VideoPlayProgressRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12#\n" +
	"\rplay_position\x18\x04 \x01(\rR\fplayPosition\x12!\n" +
	"\fis_completed\x18\x05 \x01(\rR\visCompleted\"`\n" +
	"\x14PlaylistVideoListReq\x12\x1f\n" +
	"\vplaylist_id\x18\x01 \x01(\rR\n" +
	"playlistId\x12'\n" +
	"\x04page\x18\x02 \x01(\v2\x13.common.PageRequestR\x04page\"H\n" +
	"\x11PlaylistBasicInfo\x12\x1f\n" +
	"\vplaylist_id\x18\x01 \x01(\rR\n" +
	"playlistId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\xae\x01\n" +
	"\x18PlaylistVideoListResData\x129\n" +
	"\bplaylist\x18\x01 \x01(\v2\x1d.islamic.v1.PlaylistBasicInfoR\bplaylist\x12-\n" +
	"\x04list\x18\x02 \x03(\v2\x19.islamic.v1.VideoListItemR\x04list\x12(\n" +
	"\x04page\x18\x03 \x01(\v2\x14.common.PageResponseR\x04page\"\x9b\x01\n" +
	"\x14PlaylistVideoListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x128\n" +
	"\x04data\x18\x04 \x01(\v2$.islamic.v1.PlaylistVideoListResDataR\x04data\"\x81\x01\n" +
	"\x17RecommendedVideoListReq\x12=\n" +
	"\vcategory_id\x18\x01 \x01(\v2\x1c.google.protobuf.UInt32ValueR\n" +
	"categoryId\x12'\n" +
	"\x04page\x18\x02 \x01(\v2\x13.common.PageRequestR\x04page\"v\n" +
	"\x1bRecommendedVideoListResData\x12-\n" +
	"\x04list\x18\x01 \x03(\v2\x19.islamic.v1.VideoListItemR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\xa1\x01\n" +
	"\x17RecommendedVideoListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12;\n" +
	"\x04data\x18\x04 \x01(\v2'.islamic.v1.RecommendedVideoListResDataR\x04data2\xb4\b\n" +
	"\fVideoService\x12W\n" +
	"\x11VideoPlaylistList\x12 .islamic.v1.VideoPlaylistListReq\x1a .islamic.v1.VideoPlaylistListRes\x12W\n" +
	"\x11PlaylistVideoList\x12 .islamic.v1.PlaylistVideoListReq\x1a .islamic.v1.PlaylistVideoListRes\x12W\n" +
	"\x11CategoryVideoList\x12 .islamic.v1.CategoryVideoListReq\x1a .islamic.v1.CategoryVideoListRes\x12E\n" +
	"\vVideoDetail\x12\x1a.islamic.v1.VideoDetailReq\x1a\x1a.islamic.v1.VideoDetailRes\x12`\n" +
	"\x14RecommendedVideoList\x12#.islamic.v1.RecommendedVideoListReq\x1a#.islamic.v1.RecommendedVideoListRes\x12H\n" +
	"\fVideoCollect\x12\x1b.islamic.v1.VideoCollectReq\x1a\x1b.islamic.v1.VideoCollectRes\x12i\n" +
	"\x17CheckVideoCollectStatus\x12&.islamic.v1.CheckVideoCollectStatusReq\x1a&.islamic.v1.CheckVideoCollectStatusRes\x12T\n" +
	"\x10VideoCollectList\x12\x1f.islamic.v1.VideoCollectListReq\x1a\x1f.islamic.v1.VideoCollectListRes\x12B\n" +
	"\n" +
	"VideoShare\x12\x19.islamic.v1.VideoShareReq\x1a\x19.islamic.v1.VideoShareRes\x12f\n" +
	"\x16VideoPlayHistoryRecord\x12%.islamic.v1.VideoPlayHistoryRecordReq\x1a%.islamic.v1.VideoPlayHistoryRecordRes\x12`\n" +
	"\x14VideoPlayHistoryList\x12#.islamic.v1.VideoPlayHistoryListReq\x1a#.islamic.v1.VideoPlayHistoryListRes\x12W\n" +
	"\x11VideoPlayProgress\x12 .islamic.v1.VideoPlayProgressReq\x1a .islamic.v1.VideoPlayProgressResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_video_proto_rawDescOnce sync.Once
	file_islamic_v1_video_proto_rawDescData []byte
)

func file_islamic_v1_video_proto_rawDescGZIP() []byte {
	file_islamic_v1_video_proto_rawDescOnce.Do(func() {
		file_islamic_v1_video_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_video_proto_rawDesc), len(file_islamic_v1_video_proto_rawDesc)))
	})
	return file_islamic_v1_video_proto_rawDescData
}

var file_islamic_v1_video_proto_msgTypes = make([]protoimpl.MessageInfo, 35)
var file_islamic_v1_video_proto_goTypes = []any{
	(*VideoPlaylist)(nil),               // 0: islamic.v1.VideoPlaylist
	(*VideoPlaylistListReq)(nil),        // 1: islamic.v1.VideoPlaylistListReq
	(*VideoPlaylistListResData)(nil),    // 2: islamic.v1.VideoPlaylistListResData
	(*VideoPlaylistListRes)(nil),        // 3: islamic.v1.VideoPlaylistListRes
	(*VideoListItem)(nil),               // 4: islamic.v1.VideoListItem
	(*Video)(nil),                       // 5: islamic.v1.Video
	(*CategoryVideoListReq)(nil),        // 6: islamic.v1.CategoryVideoListReq
	(*CategoryVideoListResData)(nil),    // 7: islamic.v1.CategoryVideoListResData
	(*CategoryVideoListRes)(nil),        // 8: islamic.v1.CategoryVideoListRes
	(*VideoDetailReq)(nil),              // 9: islamic.v1.VideoDetailReq
	(*VideoDetailRes)(nil),              // 10: islamic.v1.VideoDetailRes
	(*VideoCollectReq)(nil),             // 11: islamic.v1.VideoCollectReq
	(*VideoCollectRes)(nil),             // 12: islamic.v1.VideoCollectRes
	(*CheckVideoCollectStatusReq)(nil),  // 13: islamic.v1.CheckVideoCollectStatusReq
	(*CheckVideoCollectStatusRes)(nil),  // 14: islamic.v1.CheckVideoCollectStatusRes
	(*VideoCollectListReq)(nil),         // 15: islamic.v1.VideoCollectListReq
	(*VideoCollectListResData)(nil),     // 16: islamic.v1.VideoCollectListResData
	(*VideoCollectListRes)(nil),         // 17: islamic.v1.VideoCollectListRes
	(*VideoShareReq)(nil),               // 18: islamic.v1.VideoShareReq
	(*VideoShareRes)(nil),               // 19: islamic.v1.VideoShareRes
	(*VideoPlayHistory)(nil),            // 20: islamic.v1.VideoPlayHistory
	(*VideoPlayHistoryRecordReq)(nil),   // 21: islamic.v1.VideoPlayHistoryRecordReq
	(*VideoPlayHistoryRecordRes)(nil),   // 22: islamic.v1.VideoPlayHistoryRecordRes
	(*VideoPlayHistoryListReq)(nil),     // 23: islamic.v1.VideoPlayHistoryListReq
	(*VideoPlayHistoryListResData)(nil), // 24: islamic.v1.VideoPlayHistoryListResData
	(*VideoPlayHistoryListRes)(nil),     // 25: islamic.v1.VideoPlayHistoryListRes
	(*VideoPlayProgressReq)(nil),        // 26: islamic.v1.VideoPlayProgressReq
	(*VideoPlayProgressRes)(nil),        // 27: islamic.v1.VideoPlayProgressRes
	(*PlaylistVideoListReq)(nil),        // 28: islamic.v1.PlaylistVideoListReq
	(*PlaylistBasicInfo)(nil),           // 29: islamic.v1.PlaylistBasicInfo
	(*PlaylistVideoListResData)(nil),    // 30: islamic.v1.PlaylistVideoListResData
	(*PlaylistVideoListRes)(nil),        // 31: islamic.v1.PlaylistVideoListRes
	(*RecommendedVideoListReq)(nil),     // 32: islamic.v1.RecommendedVideoListReq
	(*RecommendedVideoListResData)(nil), // 33: islamic.v1.RecommendedVideoListResData
	(*RecommendedVideoListRes)(nil),     // 34: islamic.v1.RecommendedVideoListRes
	(*common.PageRequest)(nil),          // 35: common.PageRequest
	(*common.PageResponse)(nil),         // 36: common.PageResponse
	(*common.Error)(nil),                // 37: common.Error
	(*wrapperspb.UInt32Value)(nil),      // 38: google.protobuf.UInt32Value
}
var file_islamic_v1_video_proto_depIdxs = []int32{
	35, // 0: islamic.v1.VideoPlaylistListReq.page:type_name -> common.PageRequest
	0,  // 1: islamic.v1.VideoPlaylistListResData.list:type_name -> islamic.v1.VideoPlaylist
	36, // 2: islamic.v1.VideoPlaylistListResData.page:type_name -> common.PageResponse
	37, // 3: islamic.v1.VideoPlaylistListRes.error:type_name -> common.Error
	2,  // 4: islamic.v1.VideoPlaylistListRes.data:type_name -> islamic.v1.VideoPlaylistListResData
	35, // 5: islamic.v1.CategoryVideoListReq.page:type_name -> common.PageRequest
	4,  // 6: islamic.v1.CategoryVideoListResData.list:type_name -> islamic.v1.VideoListItem
	36, // 7: islamic.v1.CategoryVideoListResData.page:type_name -> common.PageResponse
	37, // 8: islamic.v1.CategoryVideoListRes.error:type_name -> common.Error
	7,  // 9: islamic.v1.CategoryVideoListRes.data:type_name -> islamic.v1.CategoryVideoListResData
	37, // 10: islamic.v1.VideoDetailRes.error:type_name -> common.Error
	5,  // 11: islamic.v1.VideoDetailRes.data:type_name -> islamic.v1.Video
	37, // 12: islamic.v1.VideoCollectRes.error:type_name -> common.Error
	37, // 13: islamic.v1.CheckVideoCollectStatusRes.error:type_name -> common.Error
	35, // 14: islamic.v1.VideoCollectListReq.page:type_name -> common.PageRequest
	4,  // 15: islamic.v1.VideoCollectListResData.list:type_name -> islamic.v1.VideoListItem
	36, // 16: islamic.v1.VideoCollectListResData.page:type_name -> common.PageResponse
	37, // 17: islamic.v1.VideoCollectListRes.error:type_name -> common.Error
	16, // 18: islamic.v1.VideoCollectListRes.data:type_name -> islamic.v1.VideoCollectListResData
	37, // 19: islamic.v1.VideoShareRes.error:type_name -> common.Error
	4,  // 20: islamic.v1.VideoPlayHistory.video:type_name -> islamic.v1.VideoListItem
	37, // 21: islamic.v1.VideoPlayHistoryRecordRes.error:type_name -> common.Error
	35, // 22: islamic.v1.VideoPlayHistoryListReq.page:type_name -> common.PageRequest
	20, // 23: islamic.v1.VideoPlayHistoryListResData.list:type_name -> islamic.v1.VideoPlayHistory
	36, // 24: islamic.v1.VideoPlayHistoryListResData.page:type_name -> common.PageResponse
	37, // 25: islamic.v1.VideoPlayHistoryListRes.error:type_name -> common.Error
	24, // 26: islamic.v1.VideoPlayHistoryListRes.data:type_name -> islamic.v1.VideoPlayHistoryListResData
	37, // 27: islamic.v1.VideoPlayProgressRes.error:type_name -> common.Error
	35, // 28: islamic.v1.PlaylistVideoListReq.page:type_name -> common.PageRequest
	29, // 29: islamic.v1.PlaylistVideoListResData.playlist:type_name -> islamic.v1.PlaylistBasicInfo
	4,  // 30: islamic.v1.PlaylistVideoListResData.list:type_name -> islamic.v1.VideoListItem
	36, // 31: islamic.v1.PlaylistVideoListResData.page:type_name -> common.PageResponse
	37, // 32: islamic.v1.PlaylistVideoListRes.error:type_name -> common.Error
	30, // 33: islamic.v1.PlaylistVideoListRes.data:type_name -> islamic.v1.PlaylistVideoListResData
	38, // 34: islamic.v1.RecommendedVideoListReq.category_id:type_name -> google.protobuf.UInt32Value
	35, // 35: islamic.v1.RecommendedVideoListReq.page:type_name -> common.PageRequest
	4,  // 36: islamic.v1.RecommendedVideoListResData.list:type_name -> islamic.v1.VideoListItem
	36, // 37: islamic.v1.RecommendedVideoListResData.page:type_name -> common.PageResponse
	37, // 38: islamic.v1.RecommendedVideoListRes.error:type_name -> common.Error
	33, // 39: islamic.v1.RecommendedVideoListRes.data:type_name -> islamic.v1.RecommendedVideoListResData
	1,  // 40: islamic.v1.VideoService.VideoPlaylistList:input_type -> islamic.v1.VideoPlaylistListReq
	28, // 41: islamic.v1.VideoService.PlaylistVideoList:input_type -> islamic.v1.PlaylistVideoListReq
	6,  // 42: islamic.v1.VideoService.CategoryVideoList:input_type -> islamic.v1.CategoryVideoListReq
	9,  // 43: islamic.v1.VideoService.VideoDetail:input_type -> islamic.v1.VideoDetailReq
	32, // 44: islamic.v1.VideoService.RecommendedVideoList:input_type -> islamic.v1.RecommendedVideoListReq
	11, // 45: islamic.v1.VideoService.VideoCollect:input_type -> islamic.v1.VideoCollectReq
	13, // 46: islamic.v1.VideoService.CheckVideoCollectStatus:input_type -> islamic.v1.CheckVideoCollectStatusReq
	15, // 47: islamic.v1.VideoService.VideoCollectList:input_type -> islamic.v1.VideoCollectListReq
	18, // 48: islamic.v1.VideoService.VideoShare:input_type -> islamic.v1.VideoShareReq
	21, // 49: islamic.v1.VideoService.VideoPlayHistoryRecord:input_type -> islamic.v1.VideoPlayHistoryRecordReq
	23, // 50: islamic.v1.VideoService.VideoPlayHistoryList:input_type -> islamic.v1.VideoPlayHistoryListReq
	26, // 51: islamic.v1.VideoService.VideoPlayProgress:input_type -> islamic.v1.VideoPlayProgressReq
	3,  // 52: islamic.v1.VideoService.VideoPlaylistList:output_type -> islamic.v1.VideoPlaylistListRes
	31, // 53: islamic.v1.VideoService.PlaylistVideoList:output_type -> islamic.v1.PlaylistVideoListRes
	8,  // 54: islamic.v1.VideoService.CategoryVideoList:output_type -> islamic.v1.CategoryVideoListRes
	10, // 55: islamic.v1.VideoService.VideoDetail:output_type -> islamic.v1.VideoDetailRes
	34, // 56: islamic.v1.VideoService.RecommendedVideoList:output_type -> islamic.v1.RecommendedVideoListRes
	12, // 57: islamic.v1.VideoService.VideoCollect:output_type -> islamic.v1.VideoCollectRes
	14, // 58: islamic.v1.VideoService.CheckVideoCollectStatus:output_type -> islamic.v1.CheckVideoCollectStatusRes
	17, // 59: islamic.v1.VideoService.VideoCollectList:output_type -> islamic.v1.VideoCollectListRes
	19, // 60: islamic.v1.VideoService.VideoShare:output_type -> islamic.v1.VideoShareRes
	22, // 61: islamic.v1.VideoService.VideoPlayHistoryRecord:output_type -> islamic.v1.VideoPlayHistoryRecordRes
	25, // 62: islamic.v1.VideoService.VideoPlayHistoryList:output_type -> islamic.v1.VideoPlayHistoryListRes
	27, // 63: islamic.v1.VideoService.VideoPlayProgress:output_type -> islamic.v1.VideoPlayProgressRes
	52, // [52:64] is the sub-list for method output_type
	40, // [40:52] is the sub-list for method input_type
	40, // [40:40] is the sub-list for extension type_name
	40, // [40:40] is the sub-list for extension extendee
	0,  // [0:40] is the sub-list for field type_name
}

func init() { file_islamic_v1_video_proto_init() }
func file_islamic_v1_video_proto_init() {
	if File_islamic_v1_video_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_video_proto_rawDesc), len(file_islamic_v1_video_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   35,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_video_proto_goTypes,
		DependencyIndexes: file_islamic_v1_video_proto_depIdxs,
		MessageInfos:      file_islamic_v1_video_proto_msgTypes,
	}.Build()
	File_islamic_v1_video_proto = out.File
	file_islamic_v1_video_proto_goTypes = nil
	file_islamic_v1_video_proto_depIdxs = nil
}
