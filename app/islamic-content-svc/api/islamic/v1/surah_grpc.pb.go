// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: islamic/v1/surah.proto

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SurahService_SurahList_FullMethodName                  = "/islamic.v1.SurahService/SurahList"
	SurahService_JuzList_FullMethodName                    = "/islamic.v1.SurahService/JuzList"
	SurahService_AyahList_FullMethodName                   = "/islamic.v1.SurahService/AyahList"
	SurahService_AyahReadRecord_FullMethodName             = "/islamic.v1.SurahService/AyahReadRecord"
	SurahService_AyahReadRecordList_FullMethodName         = "/islamic.v1.SurahService/AyahReadRecordList"
	SurahService_CheckAyahReadCollectStatus_FullMethodName = "/islamic.v1.SurahService/CheckAyahReadCollectStatus"
	SurahService_AyahReadCollect_FullMethodName            = "/islamic.v1.SurahService/AyahReadCollect"
	SurahService_AyahReadCollectList_FullMethodName        = "/islamic.v1.SurahService/AyahReadCollectList"
)

// SurahServiceClient is the client API for SurahService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SurahServiceClient interface {
	SurahList(ctx context.Context, in *SurahListReq, opts ...grpc.CallOption) (*SurahListRes, error)
	JuzList(ctx context.Context, in *JuzListReq, opts ...grpc.CallOption) (*JuzListRes, error)
	AyahList(ctx context.Context, in *AyahListReq, opts ...grpc.CallOption) (*AyahListRes, error)
	AyahReadRecord(ctx context.Context, in *AyahReadRecordReq, opts ...grpc.CallOption) (*AyahReadRecordRes, error)
	AyahReadRecordList(ctx context.Context, in *AyahReadRecordListReq, opts ...grpc.CallOption) (*AyahReadRecordListRes, error)
	CheckAyahReadCollectStatus(ctx context.Context, in *CheckAyahReadCollectStatusReq, opts ...grpc.CallOption) (*CheckAyahReadCollectStatusRes, error)
	AyahReadCollect(ctx context.Context, in *AyahReadCollectReq, opts ...grpc.CallOption) (*AyahReadCollectRes, error)
	AyahReadCollectList(ctx context.Context, in *AyahReadCollectListReq, opts ...grpc.CallOption) (*AyahReadCollectListRes, error)
}

type surahServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSurahServiceClient(cc grpc.ClientConnInterface) SurahServiceClient {
	return &surahServiceClient{cc}
}

func (c *surahServiceClient) SurahList(ctx context.Context, in *SurahListReq, opts ...grpc.CallOption) (*SurahListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SurahListRes)
	err := c.cc.Invoke(ctx, SurahService_SurahList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) JuzList(ctx context.Context, in *JuzListReq, opts ...grpc.CallOption) (*JuzListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(JuzListRes)
	err := c.cc.Invoke(ctx, SurahService_JuzList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) AyahList(ctx context.Context, in *AyahListReq, opts ...grpc.CallOption) (*AyahListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AyahListRes)
	err := c.cc.Invoke(ctx, SurahService_AyahList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) AyahReadRecord(ctx context.Context, in *AyahReadRecordReq, opts ...grpc.CallOption) (*AyahReadRecordRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AyahReadRecordRes)
	err := c.cc.Invoke(ctx, SurahService_AyahReadRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) AyahReadRecordList(ctx context.Context, in *AyahReadRecordListReq, opts ...grpc.CallOption) (*AyahReadRecordListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AyahReadRecordListRes)
	err := c.cc.Invoke(ctx, SurahService_AyahReadRecordList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) CheckAyahReadCollectStatus(ctx context.Context, in *CheckAyahReadCollectStatusReq, opts ...grpc.CallOption) (*CheckAyahReadCollectStatusRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckAyahReadCollectStatusRes)
	err := c.cc.Invoke(ctx, SurahService_CheckAyahReadCollectStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) AyahReadCollect(ctx context.Context, in *AyahReadCollectReq, opts ...grpc.CallOption) (*AyahReadCollectRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AyahReadCollectRes)
	err := c.cc.Invoke(ctx, SurahService_AyahReadCollect_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surahServiceClient) AyahReadCollectList(ctx context.Context, in *AyahReadCollectListReq, opts ...grpc.CallOption) (*AyahReadCollectListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AyahReadCollectListRes)
	err := c.cc.Invoke(ctx, SurahService_AyahReadCollectList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SurahServiceServer is the server API for SurahService service.
// All implementations must embed UnimplementedSurahServiceServer
// for forward compatibility.
type SurahServiceServer interface {
	SurahList(context.Context, *SurahListReq) (*SurahListRes, error)
	JuzList(context.Context, *JuzListReq) (*JuzListRes, error)
	AyahList(context.Context, *AyahListReq) (*AyahListRes, error)
	AyahReadRecord(context.Context, *AyahReadRecordReq) (*AyahReadRecordRes, error)
	AyahReadRecordList(context.Context, *AyahReadRecordListReq) (*AyahReadRecordListRes, error)
	CheckAyahReadCollectStatus(context.Context, *CheckAyahReadCollectStatusReq) (*CheckAyahReadCollectStatusRes, error)
	AyahReadCollect(context.Context, *AyahReadCollectReq) (*AyahReadCollectRes, error)
	AyahReadCollectList(context.Context, *AyahReadCollectListReq) (*AyahReadCollectListRes, error)
	mustEmbedUnimplementedSurahServiceServer()
}

// UnimplementedSurahServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSurahServiceServer struct{}

func (UnimplementedSurahServiceServer) SurahList(context.Context, *SurahListReq) (*SurahListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurahList not implemented")
}
func (UnimplementedSurahServiceServer) JuzList(context.Context, *JuzListReq) (*JuzListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method JuzList not implemented")
}
func (UnimplementedSurahServiceServer) AyahList(context.Context, *AyahListReq) (*AyahListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AyahList not implemented")
}
func (UnimplementedSurahServiceServer) AyahReadRecord(context.Context, *AyahReadRecordReq) (*AyahReadRecordRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AyahReadRecord not implemented")
}
func (UnimplementedSurahServiceServer) AyahReadRecordList(context.Context, *AyahReadRecordListReq) (*AyahReadRecordListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AyahReadRecordList not implemented")
}
func (UnimplementedSurahServiceServer) CheckAyahReadCollectStatus(context.Context, *CheckAyahReadCollectStatusReq) (*CheckAyahReadCollectStatusRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckAyahReadCollectStatus not implemented")
}
func (UnimplementedSurahServiceServer) AyahReadCollect(context.Context, *AyahReadCollectReq) (*AyahReadCollectRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AyahReadCollect not implemented")
}
func (UnimplementedSurahServiceServer) AyahReadCollectList(context.Context, *AyahReadCollectListReq) (*AyahReadCollectListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AyahReadCollectList not implemented")
}
func (UnimplementedSurahServiceServer) mustEmbedUnimplementedSurahServiceServer() {}
func (UnimplementedSurahServiceServer) testEmbeddedByValue()                      {}

// UnsafeSurahServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SurahServiceServer will
// result in compilation errors.
type UnsafeSurahServiceServer interface {
	mustEmbedUnimplementedSurahServiceServer()
}

func RegisterSurahServiceServer(s grpc.ServiceRegistrar, srv SurahServiceServer) {
	// If the following call pancis, it indicates UnimplementedSurahServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SurahService_ServiceDesc, srv)
}

func _SurahService_SurahList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurahListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).SurahList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurahService_SurahList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).SurahList(ctx, req.(*SurahListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_JuzList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(JuzListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).JuzList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurahService_JuzList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).JuzList(ctx, req.(*JuzListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_AyahList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AyahListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).AyahList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurahService_AyahList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).AyahList(ctx, req.(*AyahListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_AyahReadRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AyahReadRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).AyahReadRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurahService_AyahReadRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).AyahReadRecord(ctx, req.(*AyahReadRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_AyahReadRecordList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AyahReadRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).AyahReadRecordList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurahService_AyahReadRecordList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).AyahReadRecordList(ctx, req.(*AyahReadRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_CheckAyahReadCollectStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckAyahReadCollectStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).CheckAyahReadCollectStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurahService_CheckAyahReadCollectStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).CheckAyahReadCollectStatus(ctx, req.(*CheckAyahReadCollectStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_AyahReadCollect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AyahReadCollectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).AyahReadCollect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurahService_AyahReadCollect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).AyahReadCollect(ctx, req.(*AyahReadCollectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurahService_AyahReadCollectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AyahReadCollectListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurahServiceServer).AyahReadCollectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurahService_AyahReadCollectList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurahServiceServer).AyahReadCollectList(ctx, req.(*AyahReadCollectListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// SurahService_ServiceDesc is the grpc.ServiceDesc for SurahService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SurahService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.SurahService",
	HandlerType: (*SurahServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SurahList",
			Handler:    _SurahService_SurahList_Handler,
		},
		{
			MethodName: "JuzList",
			Handler:    _SurahService_JuzList_Handler,
		},
		{
			MethodName: "AyahList",
			Handler:    _SurahService_AyahList_Handler,
		},
		{
			MethodName: "AyahReadRecord",
			Handler:    _SurahService_AyahReadRecord_Handler,
		},
		{
			MethodName: "AyahReadRecordList",
			Handler:    _SurahService_AyahReadRecordList_Handler,
		},
		{
			MethodName: "CheckAyahReadCollectStatus",
			Handler:    _SurahService_CheckAyahReadCollectStatus_Handler,
		},
		{
			MethodName: "AyahReadCollect",
			Handler:    _SurahService_AyahReadCollect_Handler,
		},
		{
			MethodName: "AyahReadCollectList",
			Handler:    _SurahService_AyahReadCollectList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/surah.proto",
}
