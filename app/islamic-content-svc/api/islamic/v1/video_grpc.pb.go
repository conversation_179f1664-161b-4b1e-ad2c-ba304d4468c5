// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.31.1
// source: islamic/v1/video.proto

package islamicv1

import (
	context "context"

	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	VideoService_VideoPlaylistList_FullMethodName       = "/islamic.v1.VideoService/VideoPlaylistList"
	VideoService_PlaylistVideoList_FullMethodName       = "/islamic.v1.VideoService/PlaylistVideoList"
	VideoService_CategoryVideoList_FullMethodName       = "/islamic.v1.VideoService/CategoryVideoList"
	VideoService_VideoDetail_FullMethodName             = "/islamic.v1.VideoService/VideoDetail"
	VideoService_RecommendedVideoList_FullMethodName    = "/islamic.v1.VideoService/RecommendedVideoList"
	VideoService_VideoCollect_FullMethodName            = "/islamic.v1.VideoService/VideoCollect"
	VideoService_CheckVideoCollectStatus_FullMethodName = "/islamic.v1.VideoService/CheckVideoCollectStatus"
	VideoService_VideoCollectList_FullMethodName        = "/islamic.v1.VideoService/VideoCollectList"
	VideoService_VideoShare_FullMethodName              = "/islamic.v1.VideoService/VideoShare"
	VideoService_VideoPlayHistoryRecord_FullMethodName  = "/islamic.v1.VideoService/VideoPlayHistoryRecord"
	VideoService_VideoPlayHistoryList_FullMethodName    = "/islamic.v1.VideoService/VideoPlayHistoryList"
	VideoService_VideoPlayProgress_FullMethodName       = "/islamic.v1.VideoService/VideoPlayProgress"
)

// VideoServiceClient is the client API for VideoService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VideoServiceClient interface {
	// 视频播放列表相关
	VideoPlaylistList(ctx context.Context, in *VideoPlaylistListReq, opts ...grpc.CallOption) (*VideoPlaylistListRes, error)
	PlaylistVideoList(ctx context.Context, in *PlaylistVideoListReq, opts ...grpc.CallOption) (*PlaylistVideoListRes, error)
	// 视频相关
	CategoryVideoList(ctx context.Context, in *CategoryVideoListReq, opts ...grpc.CallOption) (*CategoryVideoListRes, error)
	VideoDetail(ctx context.Context, in *VideoDetailReq, opts ...grpc.CallOption) (*VideoDetailRes, error)
	RecommendedVideoList(ctx context.Context, in *RecommendedVideoListReq, opts ...grpc.CallOption) (*RecommendedVideoListRes, error)
	// 视频收藏相关
	VideoCollect(ctx context.Context, in *VideoCollectReq, opts ...grpc.CallOption) (*VideoCollectRes, error)
	CheckVideoCollectStatus(ctx context.Context, in *CheckVideoCollectStatusReq, opts ...grpc.CallOption) (*CheckVideoCollectStatusRes, error)
	VideoCollectList(ctx context.Context, in *VideoCollectListReq, opts ...grpc.CallOption) (*VideoCollectListRes, error)
	// 视频分享相关
	VideoShare(ctx context.Context, in *VideoShareReq, opts ...grpc.CallOption) (*VideoShareRes, error)
	// 视频播放历史相关
	VideoPlayHistoryRecord(ctx context.Context, in *VideoPlayHistoryRecordReq, opts ...grpc.CallOption) (*VideoPlayHistoryRecordRes, error)
	VideoPlayHistoryList(ctx context.Context, in *VideoPlayHistoryListReq, opts ...grpc.CallOption) (*VideoPlayHistoryListRes, error)
	VideoPlayProgress(ctx context.Context, in *VideoPlayProgressReq, opts ...grpc.CallOption) (*VideoPlayProgressRes, error)
}

type videoServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewVideoServiceClient(cc grpc.ClientConnInterface) VideoServiceClient {
	return &videoServiceClient{cc}
}

func (c *videoServiceClient) VideoPlaylistList(ctx context.Context, in *VideoPlaylistListReq, opts ...grpc.CallOption) (*VideoPlaylistListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VideoPlaylistListRes)
	err := c.cc.Invoke(ctx, VideoService_VideoPlaylistList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) PlaylistVideoList(ctx context.Context, in *PlaylistVideoListReq, opts ...grpc.CallOption) (*PlaylistVideoListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PlaylistVideoListRes)
	err := c.cc.Invoke(ctx, VideoService_PlaylistVideoList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) CategoryVideoList(ctx context.Context, in *CategoryVideoListReq, opts ...grpc.CallOption) (*CategoryVideoListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CategoryVideoListRes)
	err := c.cc.Invoke(ctx, VideoService_CategoryVideoList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoDetail(ctx context.Context, in *VideoDetailReq, opts ...grpc.CallOption) (*VideoDetailRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VideoDetailRes)
	err := c.cc.Invoke(ctx, VideoService_VideoDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) RecommendedVideoList(ctx context.Context, in *RecommendedVideoListReq, opts ...grpc.CallOption) (*RecommendedVideoListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RecommendedVideoListRes)
	err := c.cc.Invoke(ctx, VideoService_RecommendedVideoList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoCollect(ctx context.Context, in *VideoCollectReq, opts ...grpc.CallOption) (*VideoCollectRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VideoCollectRes)
	err := c.cc.Invoke(ctx, VideoService_VideoCollect_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) CheckVideoCollectStatus(ctx context.Context, in *CheckVideoCollectStatusReq, opts ...grpc.CallOption) (*CheckVideoCollectStatusRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckVideoCollectStatusRes)
	err := c.cc.Invoke(ctx, VideoService_CheckVideoCollectStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoCollectList(ctx context.Context, in *VideoCollectListReq, opts ...grpc.CallOption) (*VideoCollectListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VideoCollectListRes)
	err := c.cc.Invoke(ctx, VideoService_VideoCollectList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoShare(ctx context.Context, in *VideoShareReq, opts ...grpc.CallOption) (*VideoShareRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VideoShareRes)
	err := c.cc.Invoke(ctx, VideoService_VideoShare_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoPlayHistoryRecord(ctx context.Context, in *VideoPlayHistoryRecordReq, opts ...grpc.CallOption) (*VideoPlayHistoryRecordRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VideoPlayHistoryRecordRes)
	err := c.cc.Invoke(ctx, VideoService_VideoPlayHistoryRecord_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoPlayHistoryList(ctx context.Context, in *VideoPlayHistoryListReq, opts ...grpc.CallOption) (*VideoPlayHistoryListRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VideoPlayHistoryListRes)
	err := c.cc.Invoke(ctx, VideoService_VideoPlayHistoryList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *videoServiceClient) VideoPlayProgress(ctx context.Context, in *VideoPlayProgressReq, opts ...grpc.CallOption) (*VideoPlayProgressRes, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VideoPlayProgressRes)
	err := c.cc.Invoke(ctx, VideoService_VideoPlayProgress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VideoServiceServer is the server API for VideoService service.
// All implementations must embed UnimplementedVideoServiceServer
// for forward compatibility.
type VideoServiceServer interface {
	// 视频播放列表相关
	VideoPlaylistList(context.Context, *VideoPlaylistListReq) (*VideoPlaylistListRes, error)
	PlaylistVideoList(context.Context, *PlaylistVideoListReq) (*PlaylistVideoListRes, error)
	// 视频相关
	CategoryVideoList(context.Context, *CategoryVideoListReq) (*CategoryVideoListRes, error)
	VideoDetail(context.Context, *VideoDetailReq) (*VideoDetailRes, error)
	RecommendedVideoList(context.Context, *RecommendedVideoListReq) (*RecommendedVideoListRes, error)
	// 视频收藏相关
	VideoCollect(context.Context, *VideoCollectReq) (*VideoCollectRes, error)
	CheckVideoCollectStatus(context.Context, *CheckVideoCollectStatusReq) (*CheckVideoCollectStatusRes, error)
	VideoCollectList(context.Context, *VideoCollectListReq) (*VideoCollectListRes, error)
	// 视频分享相关
	VideoShare(context.Context, *VideoShareReq) (*VideoShareRes, error)
	// 视频播放历史相关
	VideoPlayHistoryRecord(context.Context, *VideoPlayHistoryRecordReq) (*VideoPlayHistoryRecordRes, error)
	VideoPlayHistoryList(context.Context, *VideoPlayHistoryListReq) (*VideoPlayHistoryListRes, error)
	VideoPlayProgress(context.Context, *VideoPlayProgressReq) (*VideoPlayProgressRes, error)
	mustEmbedUnimplementedVideoServiceServer()
}

// UnimplementedVideoServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedVideoServiceServer struct{}

func (UnimplementedVideoServiceServer) VideoPlaylistList(context.Context, *VideoPlaylistListReq) (*VideoPlaylistListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoPlaylistList not implemented")
}
func (UnimplementedVideoServiceServer) PlaylistVideoList(context.Context, *PlaylistVideoListReq) (*PlaylistVideoListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlaylistVideoList not implemented")
}
func (UnimplementedVideoServiceServer) CategoryVideoList(context.Context, *CategoryVideoListReq) (*CategoryVideoListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CategoryVideoList not implemented")
}
func (UnimplementedVideoServiceServer) VideoDetail(context.Context, *VideoDetailReq) (*VideoDetailRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoDetail not implemented")
}
func (UnimplementedVideoServiceServer) RecommendedVideoList(context.Context, *RecommendedVideoListReq) (*RecommendedVideoListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RecommendedVideoList not implemented")
}
func (UnimplementedVideoServiceServer) VideoCollect(context.Context, *VideoCollectReq) (*VideoCollectRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoCollect not implemented")
}
func (UnimplementedVideoServiceServer) CheckVideoCollectStatus(context.Context, *CheckVideoCollectStatusReq) (*CheckVideoCollectStatusRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckVideoCollectStatus not implemented")
}
func (UnimplementedVideoServiceServer) VideoCollectList(context.Context, *VideoCollectListReq) (*VideoCollectListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoCollectList not implemented")
}
func (UnimplementedVideoServiceServer) VideoShare(context.Context, *VideoShareReq) (*VideoShareRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoShare not implemented")
}
func (UnimplementedVideoServiceServer) VideoPlayHistoryRecord(context.Context, *VideoPlayHistoryRecordReq) (*VideoPlayHistoryRecordRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoPlayHistoryRecord not implemented")
}
func (UnimplementedVideoServiceServer) VideoPlayHistoryList(context.Context, *VideoPlayHistoryListReq) (*VideoPlayHistoryListRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoPlayHistoryList not implemented")
}
func (UnimplementedVideoServiceServer) VideoPlayProgress(context.Context, *VideoPlayProgressReq) (*VideoPlayProgressRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VideoPlayProgress not implemented")
}
func (UnimplementedVideoServiceServer) mustEmbedUnimplementedVideoServiceServer() {}
func (UnimplementedVideoServiceServer) testEmbeddedByValue()                      {}

// UnsafeVideoServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VideoServiceServer will
// result in compilation errors.
type UnsafeVideoServiceServer interface {
	mustEmbedUnimplementedVideoServiceServer()
}

func RegisterVideoServiceServer(s grpc.ServiceRegistrar, srv VideoServiceServer) {
	// If the following call pancis, it indicates UnimplementedVideoServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&VideoService_ServiceDesc, srv)
}

func _VideoService_VideoPlaylistList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoPlaylistListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoPlaylistList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_VideoPlaylistList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoPlaylistList(ctx, req.(*VideoPlaylistListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_PlaylistVideoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PlaylistVideoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).PlaylistVideoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_PlaylistVideoList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).PlaylistVideoList(ctx, req.(*PlaylistVideoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_CategoryVideoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CategoryVideoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).CategoryVideoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_CategoryVideoList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).CategoryVideoList(ctx, req.(*CategoryVideoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_VideoDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoDetail(ctx, req.(*VideoDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_RecommendedVideoList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RecommendedVideoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).RecommendedVideoList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_RecommendedVideoList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).RecommendedVideoList(ctx, req.(*RecommendedVideoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoCollect_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoCollectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoCollect(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_VideoCollect_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoCollect(ctx, req.(*VideoCollectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_CheckVideoCollectStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckVideoCollectStatusReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).CheckVideoCollectStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_CheckVideoCollectStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).CheckVideoCollectStatus(ctx, req.(*CheckVideoCollectStatusReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoCollectList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoCollectListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoCollectList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_VideoCollectList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoCollectList(ctx, req.(*VideoCollectListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoShare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoShareReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoShare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_VideoShare_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoShare(ctx, req.(*VideoShareReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoPlayHistoryRecord_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoPlayHistoryRecordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoPlayHistoryRecord(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_VideoPlayHistoryRecord_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoPlayHistoryRecord(ctx, req.(*VideoPlayHistoryRecordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoPlayHistoryList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoPlayHistoryListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoPlayHistoryList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_VideoPlayHistoryList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoPlayHistoryList(ctx, req.(*VideoPlayHistoryListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _VideoService_VideoPlayProgress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VideoPlayProgressReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VideoServiceServer).VideoPlayProgress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: VideoService_VideoPlayProgress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VideoServiceServer).VideoPlayProgress(ctx, req.(*VideoPlayProgressReq))
	}
	return interceptor(ctx, in, info, handler)
}

// VideoService_ServiceDesc is the grpc.ServiceDesc for VideoService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VideoService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "islamic.v1.VideoService",
	HandlerType: (*VideoServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "VideoPlaylistList",
			Handler:    _VideoService_VideoPlaylistList_Handler,
		},
		{
			MethodName: "PlaylistVideoList",
			Handler:    _VideoService_PlaylistVideoList_Handler,
		},
		{
			MethodName: "CategoryVideoList",
			Handler:    _VideoService_CategoryVideoList_Handler,
		},
		{
			MethodName: "VideoDetail",
			Handler:    _VideoService_VideoDetail_Handler,
		},
		{
			MethodName: "RecommendedVideoList",
			Handler:    _VideoService_RecommendedVideoList_Handler,
		},
		{
			MethodName: "VideoCollect",
			Handler:    _VideoService_VideoCollect_Handler,
		},
		{
			MethodName: "CheckVideoCollectStatus",
			Handler:    _VideoService_CheckVideoCollectStatus_Handler,
		},
		{
			MethodName: "VideoCollectList",
			Handler:    _VideoService_VideoCollectList_Handler,
		},
		{
			MethodName: "VideoShare",
			Handler:    _VideoService_VideoShare_Handler,
		},
		{
			MethodName: "VideoPlayHistoryRecord",
			Handler:    _VideoService_VideoPlayHistoryRecord_Handler,
		},
		{
			MethodName: "VideoPlayHistoryList",
			Handler:    _VideoService_VideoPlayHistoryList_Handler,
		},
		{
			MethodName: "VideoPlayProgress",
			Handler:    _VideoService_VideoPlayProgress_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "islamic/v1/video.proto",
}
