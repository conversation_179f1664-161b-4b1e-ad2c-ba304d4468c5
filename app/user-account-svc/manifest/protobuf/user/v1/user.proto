syntax = "proto3";

package user.v1;
option go_package = "halalplus/app/user-account-svc/api/user/v1;userv1";
import "common/phone_info.proto";
import "common/front_info.proto";
import "common/base.proto";

enum Gender {
  UNKNOWN = 0;
  MALE = 1;
  FEMALE = 2;
}

// UserInfo 用户信息结构
message UserInfo {
  uint64 id = 1;               // 用户id
  string account = 2;          // 账号
  int64 create_time = 3;       // 注册时间

  string nickname = 4;         // 昵称
  Gender gender = 5;           // 性别：0未知 1男 2女
  string avatar = 6;           // 头像url

  string area_code = 7; // 国家码（如 "62"）
  string phone_num  = 8; // 展示用户当前手机号码（中间部分脱敏，仅保留首尾各两位）

  bool bind_email = 9;        // 是否绑定email（0/1）
  bool bind_phone = 10;        // 是否绑定手机（0/1）
  bool bind_real_name = 11;    // 是否实名认证（0/1）

  string first_name = 12;
  string middle_name = 13;
  string last_name  = 14;
}

// 注册请求
message SignUpReq {
  string account = 1;   // 用户名/手机号/邮箱
  string password = 2;  // 密码（加密/明文视业务）
}

// 注册响应
message SignUpRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

// 登录请求
message SignInReq {
  string account = 1;   // 用户名/手机号/邮箱
  string password = 2;  // 密码
  common.FrontInfo front_info = 3;
}

// 登录响应
message UserSignInRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  UserSignInResData data = 4;
}

message UserSignInResData {
  string token = 1; // 登录成功后返回的会话 token
  UserInfo user_info = 3;
}

// 登录（用户名）
message SignInByAccountReq {
  string account = 1;   // 用户名/手机号/邮箱
  string password = 2;  // 密码
  common.FrontInfo front_info = 3;
}

message SignInByAccountRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  SignInByAccountResData data = 4;
}

message SignInByAccountResData {
  string token = 1; // 登录成功后返回的会话 token
  string secret = 2; // 客户端存储的密钥，用于api签名和更新token
  UserInfo user_info = 3;
}

// 注册/登录方式：手机号短信验证（默认）
//
// 流程：
// 1. 用户输入手机号 (交互：纯数字键盘)
//    - 国际区号：+62（目前只开放+62）
// 2. 检查手机号格式
//    - 输入时实时校验：08 开头、长度 10～12 位
// 3. 判断是否为虚拟号/黑名单（通过服务端接口验证）
// 4. 请求发送验证码
//    - 冷却控制、防多次点击（60 秒倒计时）
// 5. 输入验证码
// 6. 验证成功 → 登录成功 / 注册新用户
message SignInByPhoneReq {
  common.PhoneInfo phone_info = 1;
  string opt_code = 2;
  common.FrontInfo front_info = 3;
}

message SignInByPhoneRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  SignInByPhoneResData data = 4;
}

message SignInByPhoneResData {
  string token = 1; // 登录成功后返回的会话 token
  string secret = 2; // 客户端存储的密钥，用于api签名和更新token
  UserInfo user_info = 3;
  string session_id = 4; // 客户端存储，用于api签名和更新token
}

// 手机已注册检查
message PhoneValidCheckReq {
  common.PhoneInfo phone_info = 1;
}

message PhoneValidCheckRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}

message RefreshTokenReq {
  common.FrontInfo front_info = 3;
}

message RefreshTokenRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  RefreshTokenResData data = 4;
}

message RefreshTokenResData {
  string token = 1;
}


message UserInfoReq {
}

message UserInfoRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  UserInfoResData data = 4;
}

message UserInfoResData {
  UserInfo user_info = 3;
}

message UpdateUserInfoReq {
  string first_name = 1;
  string middle_name = 2;
  string last_name  = 3;
  string nickname = 4;         // 昵称
  Gender gender = 5;           // 性别：0未知 1男 2女
  string avatar = 6;           // 头像url
}

message UpdateUserInfoRes {
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
}
// 验证码用途场景定义
enum VerifyCodeScene {
  // 未指定场景，后端应拒绝处理该请求
  VERIFY_CODE_SCENE_UNSPECIFIED = 0;

  // 用户登录时使用的验证码（手机号登录、验证码登录）
  LOGIN = 1;

  // 用户注册时使用的验证码（注册新账号）
  SIGN_UP = 2;

  // 重置密码时使用的验证码（找回/忘记密码）
  RESET_PASSWORD = 3;

  // 绑定手机号时使用的验证码（用于增强账户安全或添加手机号）
  BIND_PHONE = 4;
}

// 验证码发送渠道
enum VerifyCodeChannel {
  // 未指定发送渠道，表示非法请求，后端应返回错误
  UNSPECIFIED = 0;

  // 通过短信（SMS）发送验证码。适用于大多数用户，尤其是国内用户。
  SMS = 1;

  // 通过 WhatsApp 发送验证码。适用于国际用户或不支持短信的区域。
  WHATSAPP = 2;

  // 通过邮箱发送验证码。适用于邮箱注册/找回密码等场景。
  EMAIL = 3;

  // 通过语音电话播报验证码。适用于用户无法接收短信时的兜底方式。
  VOICE = 4;
}

// 请求结构：发送验证码
message SendVerifyCodeReq {
  // 手机信息（包含手机号和国家码等）
  common.PhoneInfo phone_info = 1;

  // 指定发送验证码的渠道（短信、WhatsApp、语音等）
  VerifyCodeChannel verify_code_channel = 2;

  // 指定验证码使用的业务场景（登录、注册、重置密码等）
  VerifyCodeScene verify_code_scene = 3;
}

// 响应结构：发送验证码结果
message SendVerifyCodeRes {
  // 状态码，0 表示成功，其他为错误码（用于简单兼容处理）
  int32 code = 1;

  // 状态消息，一般用于错误描述或提示信息
  string msg = 2;

  // 通用错误结构，包含错误码、错误描述、详细字段错误等（可选）
  common.Error error = 3;
}

// 默认头像列表请求
message AvatarListReq {
}
// 默认头像列表响应
message AvatarListRes{
  int32 code = 1;
  string msg = 2;
  common.Error error = 3;
  AvatarListData data = 4;
}
message AvatarListData{
  repeated AvatarItem list = 1;
}

message AvatarItem {
  string name = 1;
  string key = 2;
  string url = 3;

}
// 用户服务
service UserService {
  // 发送验证码（支持短信和 WhatsApp）
  // 适用于登录、注册、找回密码等场景。
  // 限制频率：一个手机号每 60 秒只能请求一次。
  // POST /api/user-account/user/v1/UserService/SendVerifyCode
  rpc SendVerifyCode(SendVerifyCodeReq) returns (SendVerifyCodeRes);

  // 用户注册
  rpc SignUp(SignUpReq) returns (SignUpRes);

  // 用户登录
  rpc SignIn(SignInReq) returns (UserSignInRes);

  // 用户账号密码登录
  rpc SignInByAccount(SignInByAccountReq) returns (SignInByAccountRes);

  // 手机号短信验证码登录
  // POST /api/user-account/user/v1/UserService/SignIn
  rpc SignInByPhone(SignInByPhoneReq) returns (SignInByPhoneRes);

  // 手机号检查（判断是否为虚拟号/黑名单）
  //
  rpc PhoneValidCheck(PhoneValidCheckReq) returns (PhoneValidCheckRes);

  // 刷新token
  // POST /api/user-account/user/v1/UserService/RefreshToken
  rpc RefreshToken(RefreshTokenReq) returns (RefreshTokenRes);

  // 获取用户信息
  // GET /api/user-account/user/v1/UserService/UserInfo
  rpc UserInfo(UserInfoReq) returns (UserInfoRes);

  // 更新用户信息
  // POST /api/user-account/user/v1/UserService/UpdateUserInfo
  rpc UpdateUserInfo(UpdateUserInfoReq) returns (UpdateUserInfoRes);

  // 获取默认头像列表
  // GET /api/user-account/user/v1/UserService/AvatarList
  rpc AvatarList(AvatarListReq) returns (AvatarListRes);
}
